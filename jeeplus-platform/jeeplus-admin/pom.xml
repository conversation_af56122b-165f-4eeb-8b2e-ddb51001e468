<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>


    <parent>
        <groupId>org.jeeplus</groupId>
        <artifactId>jeeplus-platform</artifactId>
        <version>9.0</version>
    </parent>

    <artifactId>jeeplus-admin</artifactId>
    <packaging>jar</packaging>

    <name>jeeplus admin</name>
    <description>Admin project for jeeplus</description>


    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.jeeplus</groupId>
            <artifactId>jeeplus-common</artifactId>
            <version>${project.parent.version}</version>
        </dependency>

        <dependency>
            <groupId>org.jasig.cas.client</groupId>
            <artifactId>cas-client-core</artifactId>
            <version>3.5.1</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba.fastjson2</groupId>
            <artifactId>fastjson2</artifactId>
            <version>2.0.39</version>
        </dependency>
        <dependency>
            <groupId>org.jeeplus</groupId>
            <artifactId>fls-nc-api</artifactId>
            <version>9.0</version>
        </dependency>

        <dependency>
            <groupId>com.fls</groupId>
            <artifactId>api-sdk</artifactId>
            <version>1.0.0</version>
        </dependency>
    </dependencies>


</project>
