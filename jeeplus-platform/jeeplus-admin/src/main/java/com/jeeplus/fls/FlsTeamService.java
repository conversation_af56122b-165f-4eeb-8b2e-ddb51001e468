package com.jeeplus.fls;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpStatus;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.fls.openapi.sdk.facade.ApiClientFacade;
import com.jeeplus.sys.domain.vo.TeamVo;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * created 2023/11/10 10:41:52
 */
@Service
public class FlsTeamService {
    @Resource
    private ApiClientFacade apiClientFacade;

    public List<TeamVo> getAllTeams() {
        Map<String, Object> params = MapUtil.builder(new HashMap<String, Object>()).build();
        String result = apiClientFacade.invoke(HttpMethod.POST.name(), "/api/v1/mdms/work-team/list", params);
        JSONObject response = JSONUtil.toBean(result, JSONObject.class);
        if (response.getInt("code") != HttpStatus.HTTP_OK) {
            throw new RuntimeException("获取班组列表失败:" + response.getStr("msg"));
        }
        JSONArray dataObj = response.getJSONArray("data");
        return ObjectUtil.isNull(dataObj) ? Collections.emptyList() : JSONUtil.toList(dataObj, TeamVo.class);
    }
}
