package com.jeeplus.fls;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.jeeplus.fls.enums.LimitType;
import com.jeeplus.fls.nc.api.domain.BaseBizunit;
import com.jeeplus.fls.nc.api.domain.BasePerson;
import com.jeeplus.fls.nc.api.domain.BaseRoledata;
import com.jeeplus.fls.nc.api.domain.BaseRoledataBizunit;
import com.jeeplus.fls.nc.api.domain.BaseUser;
import com.jeeplus.fls.nc.api.domain.RoleGroup;
import com.jeeplus.fls.nc.api.domain.UserGroup;
import com.jeeplus.fls.nc.api.service.BaseBizunitService;
import com.jeeplus.fls.nc.api.service.BasePersonService;
import com.jeeplus.fls.nc.api.service.BaseRoledataBizunitService;
import com.jeeplus.fls.nc.api.service.BaseRoledataService;
import com.jeeplus.fls.nc.api.service.BaseUserService;
import com.jeeplus.fls.nc.api.service.ProcRoleGroupService;
import com.jeeplus.fls.nc.api.service.ProcUserGroupService;
import com.jeeplus.sys.constant.CommonConstants;

@Service
@DS("fls_db")
@Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
public class RoleGroupService {
	@Autowired
	private ProcRoleGroupService roleGroupService;
	
	@Autowired
	private ProcUserGroupService userGroupService;
	
	@Autowired
	private PersonService personService;
	
	@Resource
    private BasePersonService basePersonService;
	
	@Resource
    private BaseRoledataService baseRoledataService;
	
	@Resource
    private BaseRoledataBizunitService baseRoledataBizunitService;
	
	@Resource
    private BaseUserService baseUserService;
	
	@Resource
    private BaseBizunitService baseBizunitService;
	
	public List<RoleGroup> rolegroupList() {
		return roleGroupService.lambdaQuery().eq(RoleGroup::getDeleteFlag, "0")
				.eq(RoleGroup::getStatus, "2").list();
	}
	
	public RoleGroup rolegroupByCode(String code) {
		return roleGroupService.getBaseMapper().getRolegroupByCode(code);
	}
	
	public List<UserGroup> processUserGroupList() {
		return userGroupService.lambdaQuery().eq(UserGroup::getDeleteFlag, "0")
				.eq(UserGroup::getStatus, "2").list();
	}
	
	public UserGroup processUserGroupByCode(String code) {
		return userGroupService.getBaseMapper().getProcessUserGroupByCode(code);
	}
	
	public List<BaseUser> getUserByRoleGroupCode(String code, String limitType, String limitVal) {
		RoleGroup roleGroup = this.rolegroupByCode(code);
		List<BaseUser> userList = new ArrayList<BaseUser>();
		if(roleGroup == null) {
			return userList;
		}
		List<String> roleIdList = this.roleGroupService.getBaseMapper().getRoleIdsByGroupId(roleGroup.getIdRolegroup());
		if(ObjectUtils.isEmpty(roleIdList)) {
			return userList;
		}
		List<String> roleIdRes = null;
		List<BaseRoledata> roledataList = new ArrayList<>(0);
		if(!ObjectUtils.isEmpty(limitType) && !ObjectUtils.isEmpty(limitVal)) {	
			if(LimitType.ORG.getType().equals(limitType)) {
				roledataList = baseRoledataService.queryByOrgAndRoles(limitVal, roleIdList);
				if(ObjectUtils.isEmpty(roledataList)) {
					return userList;
				}
				roleIdRes = roledataList.stream().map(BaseRoledata::getIdRole).distinct().collect(Collectors.toList());
			} else if(LimitType.DEP.getType().equals(limitType)) {
				roledataList = baseRoledataService.queryByDepartAndRoles(limitVal,roleIdList);
				if(ObjectUtils.isEmpty(roledataList)) {
					return userList;
				}
				roleIdRes = roledataList.stream().map(BaseRoledata::getIdRole).distinct().collect(Collectors.toList());
			} else if(LimitType.BIZ.getType().equals(limitType)) {
                List<BaseRoledataBizunit> baseRoledataBizunits = this.baseRoledataBizunitService.queryByBizunitAndRoles(limitVal, roleIdList);
                if(ObjectUtils.isEmpty(baseRoledataBizunits)) {
                	return userList;
				}
                roleIdRes = baseRoledataBizunits.stream().map(BaseRoledataBizunit::getIdRole).distinct().collect(Collectors.toList());
			}
		}else {
			roleIdRes = roleIdList;
		}
		if (ObjectUtils.isEmpty(roleIdRes)) {
			return userList;
        }
        userList = this.personService.getUserByRoleIds(roleIdRes);
        return userList;
	}

	public List<BaseUser> getUserByProcessUserGroupCode(String code, String limitType, String limitVal) {
		UserGroup userGroup = this.userGroupService.getBaseMapper().getProcessUserGroupByCode(code);
		List<BaseUser> userList = new ArrayList<BaseUser>();
		if (userGroup == null) {
			return userList;
		}
		List<String> userIds = this.userGroupService.getBaseMapper().getUserIdsByGroupId(userGroup.getIdUsergroup());
		if (ObjectUtils.isEmpty(userIds)) {
			return userList;
        }
		List<BaseUser> baseUserList = baseUserService.lambdaQuery()
                .in(BaseUser::getIdUser, userIds)
                .eq(BaseUser::getStatus, "2")
                .eq(BaseUser::getDeleteFlag, "0")
                .list();
        if (ObjectUtils.isEmpty(baseUserList)) {
            return userList;
        }
        // 获取所有人员ID
        List<String> idPersonList = baseUserList.stream().map(BaseUser::getIdIdentity).collect(Collectors.toList());
        
        LambdaQueryChainWrapper<BasePerson>  queryWrapper = basePersonService.lambdaQuery().eq(BasePerson::getStatus, "2")
				.in(BasePerson::getIdPerson, idPersonList)
                .eq(BasePerson::getDeleteFlag, CommonConstants.NO);  
        List<BasePerson> basePersonList = null;
        if (ObjectUtils.isEmpty(limitType) || ObjectUtils.isEmpty(limitVal)) {
			basePersonList = queryWrapper.list();
		} else {
			if (LimitType.ORG.getType().equals(limitType)) {
				basePersonList = queryWrapper.eq(BasePerson::getIdOrg, limitVal).list();
			} else if (LimitType.DEP.getType().equals(limitType)) {
				basePersonList = queryWrapper.eq(BasePerson::getIdDepartment, limitVal).list();
			} else if (LimitType.BIZ.getType().equals(limitType)) {
				// 查询经营主体
				BaseBizunit baseBizunit = baseBizunitService.lambdaQuery().eq(BaseBizunit::getIdBizunit, limitVal).one();
				if (!ObjectUtils.isEmpty(baseBizunit)) {
					basePersonList = queryWrapper.eq(BasePerson::getIdOrg, baseBizunit.getIdOrg()).list();
				}
			}
		}
        if (ObjectUtils.isEmpty(basePersonList)){
			return new ArrayList<>(0);
        }
        return personService.getUserByPersonId(basePersonList.stream().map(BasePerson::getIdPerson).collect(Collectors.toList()));
	}
}
