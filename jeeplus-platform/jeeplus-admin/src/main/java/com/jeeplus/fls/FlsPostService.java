/**
 * Copyright © 2021-2026 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.fls;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jeeplus.fls.adapter.PostMapper;
import com.jeeplus.fls.enums.EnableStatus;
import com.jeeplus.fls.nc.api.domain.BasePost;
import com.jeeplus.fls.nc.api.service.BasePostService;
import com.jeeplus.sys.domain.Post;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;

/**
 * 岗位Service
 *
 * <AUTHOR>
 * @version 2021-08-17
 */
@Service
@DS("fls_db")
public class FlsPostService {

    @Resource
    private BasePostService basePostService;

    public IPage<Post> query(Post post, Page<Post> page) {
        Page<BasePost> basePostPage = new Page<>(page.getCurrent(), page.getSize());
        IPage<BasePost> result = basePostService.lambdaQuery()
                .like(!ObjectUtils.isEmpty(post.getName()), BasePost::getName, post.getName())
                .eq(!ObjectUtils.isEmpty(post.getCode()), BasePost::getCode, post.getCode())
                .eq(BasePost::getStatus, EnableStatus.ENABLE.getValue())
                .page(basePostPage);

        IPage<Post> postPage = new Page<>(result.getCurrent(), result.getSize(), result.getTotal());
        if (ObjectUtils.isEmpty(result.getRecords())){
            return postPage;
        }
        postPage.setRecords(PostMapper.INSTANCE.toPostList(result.getRecords()));
        return postPage;
    }

    public Post queryById(String id){
        BasePost basePost = basePostService.lambdaQuery().eq(BasePost::getIdPost, id).one();
        return PostMapper.INSTANCE.toPost(basePost);
    }
}
