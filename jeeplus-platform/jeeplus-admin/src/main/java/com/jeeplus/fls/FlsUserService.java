package com.jeeplus.fls;

import cn.hutool.core.map.MapUtil;
import cn.hutool.http.HttpStatus;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fls.openapi.sdk.facade.ApiClientFacade;
import com.jeeplus.fls.nc.api.domain.SysLoginUser;
import com.jeeplus.sys.service.dto.UserDTO;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * created 2023/11/10 10:41:52
 */
@Service
public class FlsUserService {
    @Resource
    private ApiClientFacade apiClientFacade;

    public IPage<UserDTO> findPageByRole(Page<UserDTO> page, UserDTO userDTO) {
        //TODO:佛朗斯角色用户关联表？?
        return new Page<>();
    }

    public SysLoginUser login(String username, String password) {
        Map<String, Object> params = MapUtil.builder(new HashMap<String, Object>()).put("username", username).put("password", password).build();
        String result = apiClientFacade.invoke(HttpMethod.POST.name(), "/api/v1/system/login", params);
        JSONObject response = JSONUtil.toBean(result, JSONObject.class);
        if (response.getInt("code") != HttpStatus.HTTP_OK) {
            throw new RuntimeException("登录失败:" + response.getStr("msg"));
        }
        SysLoginUser sysLoginUser = new SysLoginUser();
        JSONObject dataObj = response.getJSONObject("data");
        sysLoginUser.setUserId(dataObj.getStr("idUser"));
        sysLoginUser.setUserName(dataObj.getStr("name"));
        sysLoginUser.setOrgId(dataObj.getStr("idOrg"));
        sysLoginUser.setCode(dataObj.getStr("username"));
        return sysLoginUser;
    }
}
