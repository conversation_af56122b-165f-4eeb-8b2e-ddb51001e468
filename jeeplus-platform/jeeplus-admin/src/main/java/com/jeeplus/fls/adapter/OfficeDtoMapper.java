package com.jeeplus.fls.adapter;

import com.jeeplus.fls.nc.api.domain.BaseDepartment;
import com.jeeplus.fls.nc.api.domain.BaseOrg;
import com.jeeplus.sys.service.dto.OfficeDTO;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * created 2023/11/7 15:16:25
 */
@Mapper
public interface OfficeDtoMapper {
    OfficeDtoMapper INSTANCE = Mappers.getMapper ( OfficeDtoMapper.class );

    @Mapping(source = "idOrg", target = "id")
    @Mapping(source = "orgManager", target = "master")
    @Mapping(target = "createTime", ignore = true)
    OfficeDTO toOfficeDTO(BaseOrg baseOrg);


    @AfterMapping
    default void afterToOfficeDto(BaseOrg baseOrg, @MappingTarget OfficeDTO officeDTO){
        officeDTO.setType("1");
    }

    @Mapping(source = "idDepartment", target = "id")
    @Mapping(target = "createTime", ignore = true)
    OfficeDTO toOfficeDTO(BaseDepartment department);

    @AfterMapping
    default void afterToOfficeDto(BaseDepartment baseOrg, @MappingTarget OfficeDTO officeDTO){
        officeDTO.setType("2");
    }

    List<OfficeDTO> toOfficeDTOList(List<BaseOrg> baseOrgList);
    List<OfficeDTO> deptToOfficeDTOList(List<BaseDepartment> baseOrgList);


}
