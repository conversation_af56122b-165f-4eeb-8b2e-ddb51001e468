package com.jeeplus.fls.adapter;

import com.jeeplus.fls.nc.api.domain.BaseRole;
import com.jeeplus.sys.domain.Role;
import com.jeeplus.sys.service.dto.RoleDTO;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * created 2023/11/10 09:55:44
 */
@Mapper
public interface RoleMapper {
    RoleMapper INSTANCE = Mappers.getMapper ( RoleMapper.class );

    @Mapping(ignore = true, target = "createTime")
    @Mapping(source = "idRole", target = "id")
    @Mapping(source = "memo", target = "remarks")
    Role toRole(BaseRole baseRole);

    @AfterMapping
    default void afterToRole(BaseRole baseRole, @MappingTarget Role role){
        role.setUseable("1");
    }

    @Mapping(ignore = true, target = "createTime")
    @Mapping(source = "idRole", target = "id")
    @Mapping(source = "memo", target = "remarks")
    RoleDTO toRoleDto(BaseRole baseRole);

    @AfterMapping
    default void afterToRole(BaseRole baseRole, @MappingTarget RoleDTO role){
        role.setUseable("1");
    }

    List<Role> toRoleList(List<BaseRole> basePost);
}
