package com.jeeplus.fls.adapter;

import com.jeeplus.fls.nc.api.domain.BasePost;
import com.jeeplus.fls.nc.api.domain.BaseRole;
import com.jeeplus.sys.domain.Post;
import com.jeeplus.sys.domain.Role;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * created 2023/11/10 09:55:44
 */
@Mapper
public interface PostMapper {
    PostMapper INSTANCE = Mappers.getMapper ( PostMapper.class );

    @Mapping(ignore = true, target = "createTime")
    @Mapping(source = "idPost", target = "id")
    Post toPost(BasePost basePost);

    @AfterMapping
    default void afterToRole(BasePost basePost, @MappingTarget Post post){
        post.setStatus("1");
    }

    List<Post> toPostList(List<BasePost> basePost);
}
