package com.jeeplus.fls;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jeeplus.fls.adapter.RoleMapper;
import com.jeeplus.fls.enums.EnableStatus;
import com.jeeplus.fls.nc.api.domain.BaseRole;
import com.jeeplus.fls.nc.api.service.BaseRoleService;
import com.jeeplus.sys.domain.Role;
import com.jeeplus.sys.service.dto.RoleDTO;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * created 2023/11/10 10:25:38
 */
@Service
@DS("fls_db")
public class FlsRoleService {

    @Resource
    private BaseRoleService baseRoleService;

    public IPage<Role> query(Role post, Page<Role> page) {
        IPage<BaseRole> basePersonPage = new Page<>(page.getCurrent(), page.getSize());
        IPage<BaseRole> result = baseRoleService.lambdaQuery()
                .like(!ObjectUtils.isEmpty(post.getName()), BaseRole::getName, post.getName())
                .eq(BaseRole::getStatus, EnableStatus.ENABLE.getValue())
                .eq(BaseRole::getDeleteFlag, "0")
                .page(basePersonPage);

        IPage<Role> rolePage = new Page<>(result.getCurrent(), result.getSize(), result.getTotal());
        if (ObjectUtils.isEmpty(result.getRecords())) {
            return rolePage;
        }
        rolePage.setRecords(RoleMapper.INSTANCE.toRoleList(result.getRecords()));
        return rolePage;
    }

    public RoleDTO queryById(String id) {
        BaseRole baseRole = baseRoleService.lambdaQuery()
                .eq(BaseRole::getIdRole, id)
                .one();
       return RoleMapper.INSTANCE.toRoleDto(baseRole);
    }


}
