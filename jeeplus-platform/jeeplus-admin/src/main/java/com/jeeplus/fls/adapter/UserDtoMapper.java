package com.jeeplus.fls.adapter;

import com.jeeplus.fls.nc.api.domain.BasePerson;
import com.jeeplus.sys.service.dto.UserDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * created 2023/11/7 15:16:25
 */
@Mapper
public interface UserDtoMapper {
    UserDtoMapper INSTANCE = Mappers.getMapper ( UserDtoMapper.class );

    @Mapping(target = "createTime", ignore = true)
    UserDTO toUserDTO(BasePerson basePerson);

    List<UserDTO> toUserDTOList(List<BasePerson> basePersonList);
}
