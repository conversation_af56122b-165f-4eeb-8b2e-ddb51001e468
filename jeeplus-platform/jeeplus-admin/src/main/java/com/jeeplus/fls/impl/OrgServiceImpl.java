package com.jeeplus.fls.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jeeplus.fls.nc.api.service.FlsDepartmentService;
import com.jeeplus.fls.OrgService;
import com.jeeplus.fls.adapter.OfficeDtoMapper;
import com.jeeplus.fls.nc.api.domain.BaseDepartment;
import com.jeeplus.fls.nc.api.domain.BaseOrg;
import com.jeeplus.fls.nc.api.mapper.BaseOrgMapper;
import com.jeeplus.sys.service.dto.OfficeDTO;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * created 2023/11/7 14:31:09
 */
@Service
@DS("fls_db")
public class OrgServiceImpl extends ServiceImpl<BaseOrgMapper, BaseOrg> implements OrgService {
    @Resource
    private FlsDepartmentService flsDepartmentService;
    @Override
    public List<OfficeDTO> findList(String tenantId, String parentId) {
        List<BaseOrg> baseOrgList;
        if (ObjectUtils.isEmpty(parentId)){
            baseOrgList = this.lambdaQuery()
                    .isNull(BaseOrg::getIdParentorg)
                    .eq(BaseOrg::getDeleteFlag, "0")
                    .eq(BaseOrg::getStatus,"2")
                    .list();
        }else {
            baseOrgList = this.lambdaQuery()
                    .eq(BaseOrg::getIdParentorg, parentId)
                    .eq(BaseOrg::getDeleteFlag, "0")
                    .eq(BaseOrg::getStatus,"2")
                    .list();
        }
        if (ObjectUtils.isEmpty(baseOrgList)){
            return new ArrayList<>();
        }
        List<OfficeDTO> officeList = OfficeDtoMapper.INSTANCE.toOfficeDTOList(baseOrgList);
        for (OfficeDTO officeDTO : officeList) {
            findOrgByParent(officeDTO);
        }
        return officeList;
    }

    @Override
    public OfficeDTO findById(String idOrg) {
        BaseOrg baseOrg = this.lambdaQuery().eq(BaseOrg::getIdOrg, idOrg).one();
        OfficeDTO officeDTO = new OfficeDTO();
        officeDTO.setName(baseOrg.getName());
        officeDTO.setId(baseOrg.getIdOrg());
        officeDTO.setType("1");
        return officeDTO;
    }

    @Override
    public OfficeDTO findDepartmentById(String idDepartment) {
        BaseDepartment baseDepartment = flsDepartmentService.lambdaQuery()
                .eq(BaseDepartment::getIdDepartment, idDepartment)
                .one();
        OfficeDTO officeDTO = new OfficeDTO();
		if (baseDepartment != null) {
			officeDTO.setName(baseDepartment.getName());
			officeDTO.setId(baseDepartment.getIdDepartment());
			officeDTO.setType("2");
		}
        return officeDTO;
    }

    private void findOrgByParent(OfficeDTO officeDto){
       List<BaseOrg> baseOrgList = this.lambdaQuery()
               .eq(BaseOrg::getIdParentorg, officeDto.getId())
               .eq(BaseOrg::getDeleteFlag, "0")
               .eq(BaseOrg::getStatus,"2")
               .list();
       if (!ObjectUtils.isEmpty(baseOrgList)){
           List<OfficeDTO> children = OfficeDtoMapper.INSTANCE.toOfficeDTOList(baseOrgList);
           officeDto.setChildren(children);
           for (OfficeDTO baseOrg : children) {
               findOrgByParent(baseOrg);
           }
       }
       findDeptByParent(officeDto);
    }

    private void findDeptByParent(OfficeDTO officeDto){
        List<BaseDepartment> departmentList;
        if (officeDto.getType().equals("1")){
           departmentList = flsDepartmentService.lambdaQuery()
                    .eq(BaseDepartment::getIdOrg, officeDto.getId())
                    .eq(BaseDepartment::getDeptType, "0")
                    .isNull(BaseDepartment::getIdParentdept)
                    .list();
        }else {
            departmentList = flsDepartmentService.lambdaQuery()
                    .eq(BaseDepartment::getIdParentdept, officeDto.getId())
                    .eq(BaseDepartment::getDeptType, "0")
                    .list();
        }

        if (!ObjectUtils.isEmpty(departmentList)){
            List<OfficeDTO> deptChildren =  OfficeDtoMapper.INSTANCE.deptToOfficeDTOList(departmentList);
            if (ObjectUtils.isEmpty(officeDto.getChildren())){
                officeDto.setChildren(deptChildren);
            }else {
                officeDto.getChildren().addAll(deptChildren);
            }
            for (OfficeDTO deptChild : deptChildren) {
                findDeptByParent(deptChild);
            }
        }
    }



}
