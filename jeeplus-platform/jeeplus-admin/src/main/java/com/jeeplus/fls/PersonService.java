package com.jeeplus.fls;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jeeplus.fls.nc.api.domain.BasePerson;
import com.jeeplus.fls.nc.api.domain.BaseUser;
import com.jeeplus.sys.service.dto.UserDTO;

import java.util.List;

/**
 * <AUTHOR>
 * created 2023/11/7 16:17:06
 */
public interface PersonService {
    IPage<UserDTO> findPage(Page<UserDTO> page, UserDTO userDTO);

    UserDTO getByUserCode(String code);
    UserDTO getByUserId(String id);
    BasePerson getByPersonId(String id);
    List<BasePerson> getPersonByUserId(List<String> idList);
    List<BaseUser> getUserByPersonId(List<String> idList);
    List<BaseUser> getUserByPostId(String id, List<String> idOrg);
    List<BaseUser> getUserByRoleIds(List<String> ids);
    List<BaseUser> getOwerByDeptId(String id, List<String> idOrg);

    List<BaseUser> getOwerByBizUnitIds(List<String> bizUnitIds);

    List<BaseUser> getOwerByOrgId(String id);
}
