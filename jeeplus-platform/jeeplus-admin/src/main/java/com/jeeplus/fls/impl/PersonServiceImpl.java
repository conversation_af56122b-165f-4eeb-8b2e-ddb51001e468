package com.jeeplus.fls.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jeeplus.fls.OrgService;
import com.jeeplus.fls.PersonService;
import com.jeeplus.fls.adapter.UserDtoMapper;
import com.jeeplus.fls.nc.api.domain.BaseOrg;
import com.jeeplus.fls.nc.api.domain.BasePerson;
import com.jeeplus.fls.nc.api.domain.BaseUser;
import com.jeeplus.fls.nc.api.domain.SysLoginUser;
import com.jeeplus.fls.nc.api.mapper.BasePersonMapper;
import com.jeeplus.fls.nc.api.service.BaseOrgService;
import com.jeeplus.fls.nc.api.service.BasePersonService;
import com.jeeplus.fls.nc.api.service.BaseUserService;
import com.jeeplus.sys.service.dto.OfficeDTO;
import com.jeeplus.sys.service.dto.UserDTO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * created 2023/11/7 16:20:48
 */
@Service
@DS("fls_db")
public class PersonServiceImpl extends ServiceImpl<BasePersonMapper, BasePerson> implements PersonService {

    @Resource
    private OrgService orgService;

    @Resource
    private BaseOrgService baseOrgService;
    @Resource
    private BaseUserService baseUserService;

    @Resource
    private BasePersonService basePersonService;

    @Override
    public IPage<UserDTO> findPage(Page<UserDTO> page, UserDTO userDTO) {
        Page<BasePerson> personPage = new Page<>(page.getCurrent(), page.getSize());

        IPage<BasePerson> basePersonPage = this.lambdaQuery()
                .eq(!ObjectUtils.isEmpty(userDTO.getOfficeDTO().getId()), BasePerson::getIdDepartment, userDTO.getOfficeDTO().getId())
                .eq(!ObjectUtils.isEmpty(userDTO.getCompanyDTO().getId()), BasePerson::getIdOrg, userDTO.getCompanyDTO().getId())
                .eq(BasePerson::getStatus, "2")
                .like(!ObjectUtils.isEmpty(userDTO.getName()), BasePerson::getName, userDTO.getName()).page(personPage);

        IPage<UserDTO> userPage = new Page<>(basePersonPage.getCurrent(), basePersonPage.getSize(), basePersonPage.getTotal());
        List<String> idPersonList = basePersonPage.getRecords().stream().map(BasePerson::getIdPerson).collect(Collectors.toList());
        if (ObjectUtils.isEmpty(idPersonList)) {
            return userPage;
        }
        List<BaseUser> baseUserList = baseUserService.lambdaQuery()
                .in(BaseUser::getIdIdentity, idPersonList)
                .eq(BaseUser::getIdentityType, "0")
                .list();
        //1个员工可以有多个用户
        Map<String, List<BaseUser>> personToUserMap = baseUserList.stream().collect(Collectors.groupingBy(BaseUser::getIdIdentity));

        List<UserDTO> userDTOList = basePersonPage.getRecords().stream().map(it -> {
            UserDTO tmpUser = UserDtoMapper.INSTANCE.toUserDTO(it);
            if (!ObjectUtils.isEmpty(personToUserMap.get(it.getIdPerson()))) {
                tmpUser.setId(personToUserMap.get(it.getIdPerson()).get(0).getIdUser());
            }
            return tmpUser;
        }).collect(Collectors.toList());

        userPage.setRecords(userDTOList);
        return userPage;
    }

    @Override
    public UserDTO getByUserCode(String code) {
        SysLoginUser user = baseUserService.getUserInfoByCode(code);
        if (ObjectUtils.isEmpty(user)) {
            throw new RuntimeException("用户不存在");
        }
        return buildUserDto(user);
    }

    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public UserDTO getByUserId(String id) {
        SysLoginUser user = baseUserService.getUserInfoById(id);
        if (ObjectUtils.isEmpty(user)) {
            throw new RuntimeException("用户不存在");
        }
        return buildUserDto(user);
    }

    @Override
    public BasePerson getByPersonId(String id) {
        return basePersonService.lambdaQuery()
                .eq(BasePerson::getIdPerson, id)
                .eq(BasePerson::getStatus, "2")
                .eq(BasePerson::getDeleteFlag, "0")
                .one();
    }

    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public List<BasePerson> getPersonByUserId(List<String> idList) {
        List<BaseUser> baseUserList = baseUserService.lambdaQuery()
                .in(BaseUser::getIdUser, idList)
                .eq(BaseUser::getStatus, "2")
                .eq(BaseUser::getDeleteFlag, "0")
                .list();
        if (ObjectUtils.isEmpty(baseUserList)) {
            return new ArrayList<>(0);
        }
        List<String> idPersonList = baseUserList.stream().map(BaseUser::getIdIdentity).collect(Collectors.toList());
        return basePersonService.lambdaQuery()
                .in(BasePerson::getIdPerson, idPersonList)
                .eq(BasePerson::getStatus, "2")
                .eq(BasePerson::getDeleteFlag, "0")
                .list();
    }

    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public List<BaseUser> getUserByPersonId(List<String> idList) {
        return baseUserService.lambdaQuery()
                .in(BaseUser::getIdIdentity, idList)
                .eq(BaseUser::getStatus, "2")
                .eq(BaseUser::getDeleteFlag, "0")
                .list();
    }

    @Override
    public List<BaseUser> getUserByPostId(String id, List<String> idOrgs) {
        List<BasePerson> basePersonList = basePersonService.lambdaQuery()
                .eq(BasePerson::getIdPost, id)
                .in(!ObjectUtils.isEmpty(idOrgs), BasePerson::getIdOrg, idOrgs)
                .eq(BasePerson::getStatus, "2")
                .eq(BasePerson::getDeleteFlag, "0")
                .list();
        if (ObjectUtils.isEmpty(basePersonList)) {
            return new ArrayList<>(0);
        }
        return getUserByPersonId(basePersonList.stream().map(BasePerson::getIdPerson).collect(Collectors.toList()));
    }

    @Override
    public List<BaseUser> getUserByRoleIds(List<String> ids) {
        List<SysLoginUser> userList = baseUserService.getUserInfoByRoles(ids);
        if (ObjectUtils.isEmpty(userList)) {
            return new ArrayList<>(0);
        }
        return userList.stream().map(it -> {
            BaseUser baseUser = new BaseUser();
            baseUser.setIdUser(it.getUserId());
            baseUser.setName(it.getUserName());
            return baseUser;
        }).collect(Collectors.toList());
    }

    @Override
    public List<BaseUser> getOwerByDeptId(String id, List<String> idOrg) {
        //TODO:部门负责人查询
        List<BasePerson> basePersonList = basePersonService.lambdaQuery()
                .eq(BasePerson::getIdDepartment, id)
                .in(BasePerson::getIdOrg, idOrg)
                .eq(BasePerson::getStatus, "2")
                .eq(BasePerson::getDeleteFlag, "0")
                .list();
        if (ObjectUtils.isEmpty(basePersonList)) {
            return new ArrayList<>(0);
        }
        return getUserByPersonId(basePersonList.stream().map(BasePerson::getIdPerson).collect(Collectors.toList()));
    }

    @Override
    public List<BaseUser> getOwerByBizUnitIds(List<String> bizUnitIds) {
        return null;
    }

    @Override
    public List<BaseUser> getOwerByOrgId(String id) {
        BaseOrg baseOrg = baseOrgService.lambdaQuery()
                .eq(BaseOrg::getIdOrg, id)
                .eq(BaseOrg::getStatus, "2")
                .eq(BaseOrg::getDeleteFlag, "0")
                .eq(BaseOrg::getCancelFlag, "0")
                .one();
        //TODO:数据库数据为空
        String orgOwner = ObjectUtils.isEmpty(baseOrg.getOrgManager()) ? baseOrg.getOrgLeader() : baseOrg.getOrgManager();
        if (ObjectUtils.isEmpty(orgOwner)) {
            return new ArrayList<>(0);
        }

        List<BasePerson> basePersonList = basePersonService.lambdaQuery()
                .eq(BasePerson::getIdPerson, orgOwner)
                .eq(BasePerson::getStatus, "2")
                .eq(BasePerson::getDeleteFlag, "0")
                .list();
        if (ObjectUtils.isEmpty(basePersonList)) {
            return new ArrayList<>(0);
        }
        return getUserByPersonId(basePersonList.stream().map(BasePerson::getIdPerson).collect(Collectors.toList()));
    }

    private UserDTO buildUserDto(SysLoginUser user) {
        BasePerson basePerson = this.lambdaQuery()
                .eq(BasePerson::getIdPerson, user.getIdIdentity())
                .one();
        UserDTO userDTO = UserDtoMapper.INSTANCE.toUserDTO(basePerson);
        if (ObjectUtils.isEmpty(userDTO)) {
            userDTO = new UserDTO();
        }
        userDTO.setName(user.getUserName());
        userDTO.setAdmin(true);
        userDTO.setId(user.getUserId());
        if (!ObjectUtils.isEmpty(basePerson)) {
            OfficeDTO companyDTO = orgService.findById(basePerson.getIdOrg());
            OfficeDTO department = orgService.findDepartmentById(basePerson.getIdDepartment());
            userDTO.setCompanyDTO(companyDTO);
            userDTO.setOfficeDTO(department);
            userDTO.setIdPerson(basePerson.getIdPerson());
        }
        userDTO.setLoginName(user.getCode());
        return userDTO;
    }
}
