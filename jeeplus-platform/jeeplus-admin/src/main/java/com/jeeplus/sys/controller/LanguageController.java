/**
 * Copyright © 2022 - 2027 IHEPCC & NHEPSDC, Computing Center, Institute of High Energy Physics, CAS & National HEP Data Center
 */
package com.jeeplus.sys.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.jeeplus.aop.demo.annotation.DemoMode;
import com.jeeplus.core.query.QueryWrapperGenerator;
import com.jeeplus.sys.domain.Language;
import com.jeeplus.sys.service.LanguageService;
import com.jeeplus.sys.service.dto.LanguageDTO;
import com.jeeplus.sys.service.mapstruct.LanguageWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 语言Controller
 *
 * <AUTHOR>
 * @version 2021-12-12
 */
@RestController
@RequestMapping(value = "/sys/language")
public class LanguageController {

    @Autowired
    private LanguageService languageService;


    /**
     * 语言列表数据
     *
     * @param language
     * @param page
     * @return
     */
    @GetMapping("list")
    public ResponseEntity <IPage <Language>> data(Language language, Page <Language> page) throws Exception {
        QueryWrapper <Language> queryWrapper = QueryWrapperGenerator.buildQueryCondition ( language, Language.class );
        IPage <Language> result = languageService.page ( page, queryWrapper );
        return ResponseEntity.ok ( result );
    }


    /**
     * 读取多语言map
     */
    @GetMapping("getLanguageMap")
    public ResponseEntity getLanguageMap() {
        return ResponseEntity.ok ( languageService.getLanguageMap ( ) );
    }

    /**
     * 根据Id获取语言数据
     */
    @GetMapping("queryById")
    public ResponseEntity queryById(String id) {
        Language language = languageService.getById ( id );
        return ResponseEntity.ok ( language );
    }

    /**
     * 保存语言
     */
    @DemoMode
    @PostMapping("save")
    public ResponseEntity save(@Valid @RequestBody LanguageDTO languageDTO) {
        //新增或编辑表单保存
        languageService.saveOrUpdate ( LanguageWrapper.INSTANCE.toEntity ( languageDTO ) );//保存
        return ResponseEntity.ok ( "保存成功" );
    }


    /**
     * 批量删除语言
     */
    @DemoMode
    @DeleteMapping("delete")
    public ResponseEntity delete(String ids) {
        String idArray[] = ids.split ( "," );
        languageService.removeByIds ( Lists.newArrayList ( idArray ) );
        return ResponseEntity.ok ( "删除成功" );
    }

}
