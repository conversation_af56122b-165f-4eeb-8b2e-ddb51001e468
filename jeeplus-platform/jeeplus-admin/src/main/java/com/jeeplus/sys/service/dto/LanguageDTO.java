/**
 * Copyright © 2022 - 2027 IHEPCC & NHEPSDC, Computing Center, Institute of High Energy Physics, CAS & National HEP Data Center
 */
package com.jeeplus.sys.service.dto;


import com.jeeplus.core.service.dto.BaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 语言Entity
 *
 * <AUTHOR>
 * @version 2021-12-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class LanguageDTO extends BaseDTO {

    private static final long serialVersionUID = 1L;
    private String code;        // 编码
    private String zh;        // 中文
    private String en;        // 英文
    private String ja;        // 日本语

}
