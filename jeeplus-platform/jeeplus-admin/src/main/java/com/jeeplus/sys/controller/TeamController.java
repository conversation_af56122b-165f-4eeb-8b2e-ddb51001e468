/**
 * Copyright © 2021-2026 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.sys.controller;

import com.jeeplus.fls.FlsTeamService;
import com.jeeplus.sys.domain.vo.TeamVo;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;


/**
 * 工作班组列表查询
 *
 * <AUTHOR>
 * @since 2025-01-06
 */
@RestController
@RequestMapping(value = "/sys/team")
public class TeamController {

    @Resource
    private FlsTeamService teamService;

    /**
     * 获取岗位列表数据
     */
    @GetMapping("list")
    public ResponseEntity list() throws Exception {
        List<TeamVo> allTeams = teamService.getAllTeams();
        return ResponseEntity.ok(allTeams);
    }
}
