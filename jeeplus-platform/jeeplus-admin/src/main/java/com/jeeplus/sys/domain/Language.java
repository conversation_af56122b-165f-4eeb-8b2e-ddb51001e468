/**
 * Copyright © 2022 - 2027 IHEPCC & NHEPSDC, Computing Center, Institute of High Energy Physics, CAS & National HEP Data Center
 */
package com.jeeplus.sys.domain;


import com.baomidou.mybatisplus.annotation.TableName;
import com.jeeplus.core.domain.BaseEntity;
import com.jeeplus.core.query.Query;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 语言Entity
 *
 * <AUTHOR>
 * @version 2021-12-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("sys_language")
public class Language extends BaseEntity {

    private static final long serialVersionUID = 1L;
    @Query
    private String code;        // 编码
    @Query
    private String zh;        // 中文
    private String en;        // 英文
    private String ja;        // 日本语

}
