package com.jeeplus.sys.service.mapstruct;

import com.jeeplus.core.mapstruct.EntityWrapper;
import com.jeeplus.sys.domain.Language;
import com.jeeplus.sys.service.dto.LanguageDTO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;


@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, uses = {})
public interface LanguageWrapper extends EntityWrapper <LanguageDTO, Language> {

    LanguageWrapper INSTANCE = Mappers.getMapper ( LanguageWrapper.class );
}
