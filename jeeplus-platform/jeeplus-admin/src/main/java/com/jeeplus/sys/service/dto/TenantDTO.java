/**
 * Copyright © 2021-2026 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.sys.service.dto;

import com.jeeplus.core.query.Query;
import com.jeeplus.core.service.dto.BaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 岗位Entity
 *
 * <AUTHOR>
 * @version 2020-08-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class TenantDTO extends BaseDTO {

    private static final long serialVersionUID = 1L;
    /**
     * 租户名
     */
    @Query
    private String name;

    /**
     * 租户颜色
     */
    private String color;
    /**
     * 租户开始日期
     */
    private Date beginDate;
    /**
     * 租户结束日期
     */
    private Date endDate;
    /**
     * 租户状态
     */
    private String status;
    /**
     * 绑定域名
     */
    private String domain;


}
