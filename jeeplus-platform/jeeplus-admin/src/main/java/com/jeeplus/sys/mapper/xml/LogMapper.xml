<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jeeplus.sys.mapper.LogMapper">

    <select id="findList" resultType="com.jeeplus.sys.service.dto.LogDTO">
        SELECT a.id           AS "id",
               a.type         AS "type",
               a.title        AS "title",
               a.remote_addr  AS "remoteAddr",
               a.request_uri  AS "requestUri",
               a.request_type AS "requestType",
               a.method       AS "method",
               a.params       AS "params",
               a.result       AS "result",
               a.user_agent   AS "userAgent",
               a.exception    AS "exception",
               a.record_time  AS "recordTime",
               a.create_time  AS "createTime",
               u.id           AS "createBy.id",
               u.name         AS "createBy.name",
               c.name         AS "createBy.companyDTO.name",
               o.name         AS "createBy.officeDTO.name"
        FROM sys_log a
                 left JOIN sys_user u ON u.id = a.create_by_id
                 left JOIN sys_office c ON c.id = u.company_id
                 left JOIN sys_office o ON o.id = u.office_id
            ${ew.customSqlSegment}
    </select>


</mapper>
