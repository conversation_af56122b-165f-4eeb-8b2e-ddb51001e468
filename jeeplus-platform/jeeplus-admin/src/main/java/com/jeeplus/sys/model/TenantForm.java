package com.jeeplus.sys.model;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
public class TenantForm {
    private BasicForm basicForm = new BasicForm ( );
    private UserForm userForm = new UserForm ( );
    private RoleForm roleForm = new RoleForm ( );
    private ConfigForm configForm = new ConfigForm ( );

    @Data
    @EqualsAndHashCode(callSuper = false)
    public class BasicForm {
        private String id;
        private String name;
        private String code;
        private Date beginDate;
        private Date endDate;
        private String color;
        private String status;
        private String domain;
    }

    @Data
    @EqualsAndHashCode(callSuper = false)
    public class ConfigForm {
        private String defaultTheme;
        private String productName;
        private String logo;
        private String defaultLayout;
        private String defaultColor;
    }


    @Data
    @EqualsAndHashCode(callSuper = false)
    public class UserForm {
        private String loginName; // 登录名
        private String name; // 姓名
        private String email; // 邮箱
        private String phone; // 电话
        private String mobile; // 手机
        private String newPassword; // 新密码
        private String confirmNewPassword;
        private boolean isEdit;
    }

    @Data
    @EqualsAndHashCode(callSuper = false)
    public class RoleForm {
        private String menuIds;
        private String name;
        private String enName;
    }


}


