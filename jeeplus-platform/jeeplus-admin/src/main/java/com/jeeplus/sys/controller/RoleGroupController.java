package com.jeeplus.sys.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.jeeplus.aop.logging.annotation.ApiLog;
import com.jeeplus.fls.RoleGroupService;
import com.jeeplus.fls.nc.api.domain.RoleGroup;
import com.jeeplus.fls.nc.api.domain.UserGroup;

@RestController
@RequestMapping("/sys")
public class RoleGroupController {

	@Autowired
	private RoleGroupService roleGroupService;

	@ApiLog("查询角色组列表")
	@GetMapping("/roleGroup/list")
	public ResponseEntity<List<RoleGroup>> rolegroupList() throws Exception {
		return ResponseEntity.ok(roleGroupService.rolegroupList());
	}

	@ApiLog("查询角色组")
	@GetMapping("/roleGroup/code/{code}")
	public ResponseEntity<RoleGroup> rolegroupByCode(@PathVariable("code") String code) throws Exception {
		return ResponseEntity.ok(roleGroupService.rolegroupByCode(code));
	}

	@ApiLog("查询程序用户组列表")
	@GetMapping("/processUserGroup/list")
	public ResponseEntity<List<UserGroup>> processUserGroupList() throws Exception {
		return ResponseEntity.ok(roleGroupService.processUserGroupList());
	}

	@ApiLog("查询程序用户组")
	@GetMapping("/processUserGroup/code/{code}")
	public ResponseEntity<UserGroup> processUserGroupByCode(@PathVariable("code") String code) throws Exception {
		return ResponseEntity.ok(roleGroupService.processUserGroupByCode(code));
	}
}
