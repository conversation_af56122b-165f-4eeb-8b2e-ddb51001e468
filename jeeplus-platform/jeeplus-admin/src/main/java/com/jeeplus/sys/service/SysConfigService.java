/**
 * Copyright &copy; 2021-2026 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.sys.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jeeplus.common.redis.RedisUtils;
import com.jeeplus.sys.constant.CacheNames;
import com.jeeplus.sys.domain.SysConfig;
import com.jeeplus.sys.mapper.SysConfigMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 系统配置Service
 *
 * <AUTHOR>
 * @version 2021-09-18
 */
@Service
@Transactional
public class SysConfigService extends ServiceImpl <SysConfigMapper, SysConfig> {


    public SysConfig getByTenantId(String tenantId) {
        SysConfig sysConfig = (SysConfig) RedisUtils.getInstance ( ).get ( CacheNames.SYS_CACHE_CONFIG, tenantId );
        if ( sysConfig == null ) {
            sysConfig = super.lambdaQuery ( ).eq ( SysConfig::getTenantId, tenantId ).one ( );
            RedisUtils.getInstance ( ).set ( CacheNames.SYS_CACHE_CONFIG, tenantId, sysConfig );
        }
        return sysConfig;
    }

    public boolean updateByTenantId(SysConfig sysConfig) {
        RedisUtils.getInstance ( ).delete ( CacheNames.SYS_CACHE_CONFIG, sysConfig.getTenantId ( ) );
        return super.lambdaUpdate ( ).eq ( SysConfig::getTenantId, sysConfig.getTenantId ( ) ).update ( sysConfig );
    }


}
