/**
 * Copyright © 2022 - 2027 IHEPCC & NHEPSDC, Computing Center, Institute of High Energy Physics, CAS & National HEP Data Center
 */
package com.jeeplus.sys.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jeeplus.common.redis.RedisUtils;
import com.jeeplus.sys.constant.CacheNames;
import com.jeeplus.sys.domain.Language;
import com.jeeplus.sys.mapper.LanguageMapper;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 语言Service
 *
 * <AUTHOR>
 * @version 2021-12-12
 */
@Service
public class LanguageService extends ServiceImpl <LanguageMapper, Language> {

    public Map getLanguageMap() {
        Map languageMap = (Map <String, String>) RedisUtils.getInstance ( ).get ( CacheNames.SYS_CACHE_LANGUAGE_MAP );
        if ( languageMap == null ) {
            languageMap = new HashMap ( );
            List <Language> list = super.list ( );
            for (Language language : list) {
                languageMap.put ( language.getCode ( ) + "_zh", language.getZh ( ) );
                languageMap.put ( language.getCode ( ) + "_en", language.getEn ( ) );
                languageMap.put ( language.getCode ( ) + "_ja", language.getJa ( ) );
            }
        }
        return languageMap;
    }

    @CacheEvict(cacheNames = CacheNames.SYS_CACHE_LANGUAGE_MAP, allEntries = true)
    public boolean save(Language language) {
        return super.save ( language );
    }

    @CacheEvict(cacheNames = CacheNames.SYS_CACHE_LANGUAGE_MAP, allEntries = true)
    public void delete(Language language) {
        super.removeById ( language.getId ( ) );
    }

}
