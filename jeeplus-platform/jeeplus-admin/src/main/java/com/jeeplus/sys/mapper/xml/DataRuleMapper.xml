<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jeeplus.sys.mapper.DataRuleMapper">

    <select id="findByUserId" resultType="com.jeeplus.sys.service.dto.DataRuleDTO">
        SELECT DISTINCT
        a.id AS "id",
        a.menu_id AS "menuId",
        a.name AS "name",
        a.class_name AS "className",
        a.t_field AS "field",
        a.t_express AS "express",
        a.t_value AS "value",
        <choose>
            <when test="_databaseId == 'oracle'">
                TO_CHAR(a.sql_segment) as sqlSegment,
            </when>
            <otherwise>
                a.sql_segment AS "sqlSegment",
            </otherwise>
        </choose>
        a.remarks AS "remarks",
        a.del_flag AS "delFlag"
        FROM sys_datarule a
        JOIN sys_role_datarule rd ON rd.datarule_id = a.id
        JOIN sys_role r ON r.id = rd.role_id AND r.useable='1'
        JOIN sys_user_role ur ON ur.role_id = r.id
        JOIN sys_user u ON u.id = ur.user_id AND u.id = #{userId}
        WHERE a.del_flag = 0 AND r.del_flag = 0 AND u.del_flag = 0
    </select>


    <delete id="deleteRoleDataRule">
        DELETE
        FROM sys_role_datarule
        WHERE datarule_id = #{dataRuleId}
    </delete>
</mapper>
