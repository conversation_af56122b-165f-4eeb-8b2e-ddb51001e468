package com.jeeplus.sys.service.mapstruct;

import com.jeeplus.core.mapstruct.TreeWrapper;
import com.jeeplus.sys.domain.Office;
import com.jeeplus.sys.service.dto.OfficeDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, uses = {})
public interface OfficeWrapper extends TreeWrapper <OfficeDTO, Office> {

    OfficeWrapper INSTANCE = Mappers.getMapper ( OfficeWrapper.class );

    /**
     * dto对象转化成entity对象
     */
    @Mappings({
            @Mapping(source = "tenantDTO.id", target = "tenantId"),
            @Mapping(source = "parent.id", target = "parentId"),
            @Mapping(source = "createBy.id", target = "createById"),
            @Mapping(source = "updateBy.id", target = "updateById")})
    Office toEntity(OfficeDTO dto);

    /**
     * entity对象转换成dto对象
     */
    @Mappings({
            @Mapping(source = "tenantId", target = "tenantDTO.id"),
            @Mapping(source = "parentId", target = "parent.id"),
            @Mapping(source = "createById", target = "createBy.id"),
            @Mapping(source = "updateById", target = "updateBy.id")})
    OfficeDTO toDTO(Office entity);

}
