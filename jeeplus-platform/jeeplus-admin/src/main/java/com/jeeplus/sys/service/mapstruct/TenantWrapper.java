package com.jeeplus.sys.service.mapstruct;

import com.jeeplus.core.mapstruct.EntityWrapper;
import com.jeeplus.sys.domain.Tenant;
import com.jeeplus.sys.service.dto.TenantDTO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, uses = {})
public interface TenantWrapper extends EntityWrapper <TenantDTO, Tenant> {

    TenantWrapper INSTANCE = Mappers.getMapper ( TenantWrapper.class );
}
