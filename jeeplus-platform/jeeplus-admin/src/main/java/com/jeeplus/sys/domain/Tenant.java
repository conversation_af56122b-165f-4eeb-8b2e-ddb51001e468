/**
 * Copyright © 2021-2026 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.sys.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jeeplus.core.domain.BaseEntity;
import com.jeeplus.core.query.Query;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 岗位Entity
 *
 * <AUTHOR>
 * @version 2020-08-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("sys_tenant")
public class Tenant extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 实体主键
     */
    @TableId(type = IdType.INPUT)
    private String id;
    /**
     * 租户颜色
     */
    private String color;
    /**
     * 租户名
     */
    @Query
    private String name;
    /**
     * 租户开始日期
     */
    private Date beginDate;
    /**
     * 租户结束日期
     */
    private Date endDate;
    /**
     * 租户状态
     */
    private String status;
    /**
     * 绑定域名
     */
    private String domain;
    /**
     * 获取不存在的字段
     */
    @TableField(exist = false)
    @Deprecated
    private String tenantId;


}
