/**
 * Copyright &copy; 2021-2026 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.sys.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.jeeplus.core.domain.TreeMapper;
import com.jeeplus.sys.domain.Menu;
import com.jeeplus.sys.service.dto.MenuDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 菜单MAPPER接口
 *
 * <AUTHOR>
 * @version 2021-05-16
 */
public interface MenuMapper extends TreeMapper <Menu> {

    List <MenuDTO> findByUserId(MenuDTO menuDTO);

    void deleteMenuRole(@Param("menuId") String menuId);

    List <MenuDTO> findAllWithDataRuleList();

    List <String> mrList(@Param(Constants.WRAPPER) Wrapper wrapper);

    List <String> mdList(@Param(Constants.WRAPPER) Wrapper wrapper);

}
