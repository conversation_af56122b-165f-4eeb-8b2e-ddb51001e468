package com.jeeplus.security.jwt;

import com.alibaba.fastjson2.JSON;
import com.jeeplus.common.redis.RedisUtils;
import com.jeeplus.fls.nc.api.domain.SysLoginUser;
import com.jeeplus.fls.nc.api.utils.JwtPayLoad;
import com.jeeplus.fls.nc.api.utils.JwtTokenUtil;
import com.jeeplus.security.util.UserSessionDto;
import com.jeeplus.security.util.UserSessionUtils;
import com.jeeplus.sys.service.dto.UserDTO;
import com.jeeplus.sys.utils.TenantUtils;
import com.jeeplus.sys.utils.UserUtils;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.User;
import org.springframework.util.ObjectUtils;
import org.springframework.web.filter.GenericFilterBean;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * JWT过滤器
 */
public class FlsJWTFilter extends GenericFilterBean {

    public static final String PC_SOURCE = "pc";

    public static final String APPLET_SOURCE = "applet";

    private final TokenProvider tokenProvider;

    AuthenticationManager authenticationManager;


    public FlsJWTFilter(TokenProvider tokenProvider, AuthenticationManager authenticationManager) {
        this.tokenProvider = tokenProvider;
        this.authenticationManager = authenticationManager;
    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain)
            throws IOException, ServletException {

        HttpServletRequest request = (HttpServletRequest) servletRequest;
        String token = request.getHeader("token");
        String clientSource = request.getHeader("client-source");
        if (ObjectUtils.isEmpty(clientSource) || ObjectUtils.isEmpty(token)) {
            filterChain.doFilter(servletRequest, servletResponse);
            return;
        }
        // 小程序只有token，没有ticket
        Authentication authentication;
        SysLoginUser sysLoginUser;
        if (APPLET_SOURCE.equals(clientSource)) { // 小程序
            JwtPayLoad jwtPayLoad = JwtTokenUtil.getJwtPayLoad(token);
            authentication = getFlsAuthenticationUserId(jwtPayLoad.getId_user(), token);
            String userinfo = (String) RedisUtils.getInstance().get(APPLET_SOURCE + ":" + token);
            if (ObjectUtils.isEmpty(userinfo)) {
                throw new RuntimeException("token过期");
            }
            sysLoginUser = JSON.parseObject(userinfo, SysLoginUser.class);
        } else if (PC_SOURCE.equals(clientSource)) {
            String userStr = (String) RedisUtils.getInstance().get(PC_SOURCE + ":" + token);
            if (ObjectUtils.isEmpty(userStr)) {
                throw new RuntimeException("token过期");
            }
            sysLoginUser = JSON.parseObject(userStr, SysLoginUser.class);
            authentication = getFlsAuthentication(sysLoginUser.getCode(), token);
//            authentication = getFlsAuthentication("90040", token);
//            authentication = getFlsAuthentication("90041", token);
        } else {
            throw new RuntimeException("header,client-source参数异常");
        }
        UserSessionDto sessionDto = new UserSessionDto();
        sessionDto.setToken(token);
        sessionDto.setIdUser(sysLoginUser.getUserId());
        sessionDto.setClientSource(clientSource);
        UserSessionUtils.put(sessionDto);
        SecurityContextHolder.getContext().setAuthentication(authentication);
        filterChain.doFilter(servletRequest, servletResponse);
    }

    public static Authentication getFlsAuthenticationUserId(String userId, String token) {
        UserDTO userDTO = UserUtils.get(userId);
        return getFlsAuthentication(userDTO.getLoginName(), token);
    }

    public static Authentication getFlsAuthentication(String userCode, String token) {
        //放开所有权限，具体权限由接入放控制
        Set<String> permissions = UserUtils.getPermissionsByLoginName(userCode, TenantUtils.getTenantId());
        // 添加基于Permission的权限信息
        List<GrantedAuthority> authorities = new ArrayList<>();
        for (String permission : permissions) {
            authorities.add(new SimpleGrantedAuthority(permission));
        }
        User principal = new User(userCode, "", authorities);
        return new UsernamePasswordAuthenticationToken(principal, token, authorities);
    }

}
