package com.jeeplus.security.util;

import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.jeeplus.common.redis.RedisUtils;
import com.jeeplus.fls.FlsUserService;
import com.jeeplus.fls.nc.api.domain.SysLoginUser;
import com.jeeplus.fls.nc.api.utils.JwtTokenUtil;
import com.jeeplus.sys.utils.TenantUtils;
import com.jeeplus.sys.utils.UserUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.AnonymousAuthenticationToken;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

@Slf4j
public class SecurityUtils {

    /**
     * 描述根据账号密码进行调用security进行认证授权 主动调
     * 用AuthenticationManager的authenticate方法实现
     * 授权成功后将用户信息存入SecurityContext当中
     *
     * @param username              用户名
     * @param password              密码
     * @param authenticationManager 认证授权管理器,
     * @return UserInfo  用户信息
     * @see AuthenticationManager
     */
    public static Authentication login(String username, String password, AuthenticationManager authenticationManager) throws AuthenticationException {
        //使用security框架自带的验证token生成器  也可以自定义。
        UsernamePasswordAuthenticationToken token = new UsernamePasswordAuthenticationToken(username, password);
        //该方法会去调用userDetailsService.loadUserByUsername()去验证用户名和密码，如果正确，则存储该用户名密码到“security 的 context中”
        Authentication authentication = authenticationManager.authenticate(token);
        SecurityContextHolder.getContext().setAuthentication(authentication);
//        User userInfo = (User) authentication.getPrincipal();
        return authentication;
    }

    public static void customLogin(String username, String password) {
        SysLoginUser user = SpringUtil.getBean(FlsUserService.class).login(username, password);
        String userStr = JSONUtil.parseObj(user, false).toStringPretty();
        // 放到redis中 默认是24小时
        String code = user.getCode();
        //放开所有权限，具体权限由接入放控制
        Set<String> permissions = UserUtils.getPermissionsByLoginName(code, TenantUtils.getTenantId());
        // 添加基于Permission的权限信息
        List<GrantedAuthority> authorities = new ArrayList<>();
        permissions.forEach(permission -> authorities.add(new SimpleGrantedAuthority(permission)));
        User principal = new User(code, "", authorities);
        String token = JwtTokenUtil.sign(user.getUserId(), "pc");
        Authentication authentication = new UsernamePasswordAuthenticationToken(principal, token, authorities);
        SecurityContextHolder.getContext().setAuthentication(authentication);
        RedisUtils.getInstance().set("pc" + ":" + token, userStr, 86400);
    }


    /**
     * 获取当前登录的所有认证信息
     *
     * @return
     */
    public static Authentication getAuthentication() {

        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (!(authentication instanceof AnonymousAuthenticationToken)) {
            return authentication;
        }

        return null;
    }

    /**
     * 获取当前登录的token
     *
     * @return
     */
    public static String getToken() {

        Authentication authentication = getAuthentication();
        return authentication != null ? authentication.getCredentials().toString() : null;
    }


    /**
     * 获取当前登录用户名
     *
     * @return
     */
    public static String getLoginName() {
        Authentication authentication = getAuthentication();
        return authentication != null ? authentication.getName() : null;
    }

    /**
     * 生成BCryptPasswordEncoder密码
     *
     * @param password 密码
     * @return 加密字符串
     */
    public static String encryptPassword(String password) {
        BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
        return passwordEncoder.encode(password);
    }

    /**
     * 验证密码是否正确
     *
     * @param plainPassword
     * @param password
     * @return
     */
    public static boolean validatePassword(String plainPassword, String password) {
        BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
        return passwordEncoder.matches(plainPassword, password);
    }

}
