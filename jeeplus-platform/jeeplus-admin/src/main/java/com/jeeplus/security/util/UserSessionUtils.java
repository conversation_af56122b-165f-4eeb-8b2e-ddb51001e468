package com.jeeplus.security.util;

import org.springframework.beans.BeanUtils;

/**
 * <AUTHOR>
 * created 2023/11/27 21:49:17
 */
public class UserSessionUtils {
    private static final ThreadLocal<UserSessionDto> THREAD_LOCAL_SESSION = ThreadLocal.withInitial(UserSessionDto::new);

    public static UserSessionDto getCurrent(){
        return THREAD_LOCAL_SESSION.get();
    }

    public static void put(UserSessionDto userSessionDto){
        UserSessionDto session = THREAD_LOCAL_SESSION.get();
        BeanUtils.copyProperties(userSessionDto, session);
    }
}
