package com.jeeplus.core.mapstruct;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.List;

public interface EntityWrapper<D, E> {

    @Mappings({
            @Mapping(source = "tenantDTO.id", target = "tenantId"),
            @Mapping(source = "createBy.id", target = "createById"),
            @Mapping(source = "updateBy.id", target = "updateById")})
    E toEntity(D dto);


    @Mappings({
            @Mapping(source = "tenantId", target = "tenantDTO.id"),
            @Mapping(source = "createById", target = "createBy.id"),
            @Mapping(source = "updateById", target = "updateBy.id")})
    D toDTO(E entity);

    List <E> toEntity(List <D> dtoList);


    List <D> toDTO(List <E> entityList);


    Page <E> toEntity(Page <D> page);

    Page <D> toDTO(Page <E> page);


}
