package com.jeeplus.common.converter;

import cn.hutool.core.util.StrUtil;
import org.springframework.core.convert.converter.Converter;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

public class StringToDateConverter implements Converter <String, Date> {

    @Override
    public Date convert(String source) {
        Date target = null;
        if ( !StrUtil.isEmpty ( source ) ) {
            SimpleDateFormat format = new SimpleDateFormat ( "yyyy-MM-dd HH:mm:ss" );
            try {
                target = format.parse ( source );
            } catch (ParseException e) {
                throw new RuntimeException ( String.format ( "parser %s to Date fail", source ) );
            }
        }
        return target;
    }
}
