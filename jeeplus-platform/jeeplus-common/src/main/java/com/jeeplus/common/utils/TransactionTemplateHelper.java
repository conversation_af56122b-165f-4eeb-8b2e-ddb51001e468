package com.jeeplus.common.utils;

import org.springframework.stereotype.Component;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionCallbackWithoutResult;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;

/**
 * 编程式事务
 * <AUTHOR>
 * @date 2023/2/3
 * @since 1.0.0
 */
@Component
public class TransactionTemplateHelper {

    @Resource
    private PlatformTransactionManager transactionManager;

    private TransactionTemplate getTransactionTemplate(PlatformTransactionManager txManager, int propagationBehavior, int isolationLevel)
    {
        TransactionTemplate transactionTemplate = new TransactionTemplate(txManager);
        transactionTemplate.setPropagationBehavior(propagationBehavior);
        transactionTemplate.setIsolationLevel(isolationLevel);
        return transactionTemplate;
    }

    public TransactionTemplate getDefaultTransactionTemplate(PlatformTransactionManager txManager) {
        return getTransactionTemplate(txManager, TransactionDefinition.PROPAGATION_REQUIRED, TransactionDefinition.ISOLATION_READ_COMMITTED);
    }


    public <T> T execute(TransactionCallback<T> callback){
        return getDefaultTransactionTemplate(transactionManager).execute(callback);
    }

    public <T> void execute(TransactionCallbackWithoutResult callback){
        getDefaultTransactionTemplate(transactionManager).execute(callback);
    }

}
