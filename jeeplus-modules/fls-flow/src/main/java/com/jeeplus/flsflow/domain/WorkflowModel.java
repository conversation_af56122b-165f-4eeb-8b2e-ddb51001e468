package com.jeeplus.flsflow.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jeeplus.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * created 2023/11/10 15:01:48
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("t_workflow_model")
public class WorkflowModel extends BaseEntity {
    /**
     *  varchar(36) NOT NULL COMMENT '流程模型id',
     */
    private String flowableModelId;
    /**
     *  varchar(50) NOT NULL COMMENT '流程模型key',
     */
    private String flowableModelKey;
    /**
     *  varchar(36) NOT NULL COMMENT '流程名称',
     */
    private String name;
    /**
     *  varchar(50) NOT NULL COMMENT '业务类型:purchase_review外采,repair_log维修日志',
     */
    private String workflowType;

    /**
     * 1-启用,2-禁用
     */
    private Integer status;
}
