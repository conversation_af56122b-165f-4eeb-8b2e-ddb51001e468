package com.jeeplus.flsflow.service.impl;

import com.jeeplus.flsflow.service.FlsFlowService;
import com.jeeplus.sys.utils.UserUtils;
import org.flowable.engine.TaskService;
import org.flowable.task.api.Task;
import org.flowable.task.api.TaskQuery;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * created 2023/11/8 17:49:21
 */
@Service
public class FlsFlowServiceImpl implements FlsFlowService {

    @Resource
    private TaskService taskService;

    @Override
    public Task queryTaskByProcessInsId(String insId, String userId) {
        // =============== 已经签收或者等待签收的任务  ===============
        TaskQuery todoTaskQuery = taskService.createTaskQuery ()
                .taskCandidateOrAssigned( userId ).active()
                .includeProcessVariables( )
                .processInstanceId(insId)
                .orderByTaskCreateTime ( ).desc ( );
        List<Task> taskList = todoTaskQuery.list();
        if (ObjectUtils.isEmpty(taskList)){
            return null;
        }
        if (taskList.size() > 1){
            throw new RuntimeException("存在多个待办任务");
        }
        return taskList.get(0);
    }


    @Override
    public Task getTaskByProcessInsId(String insId) {
        // =============== 查询任务  ===============
        TaskQuery todoTaskQuery = taskService.createTaskQuery ()
                .includeProcessVariables( )
                .processInstanceId(insId)
                .orderByTaskCreateTime ( ).desc ( );
        List<Task> taskList = todoTaskQuery.list();
        if (ObjectUtils.isEmpty(taskList)){
            return null;
        }
        if (taskList.size() > 1){
            String userId = UserUtils.getCurrentUserDTO().getId();
            for(Task task:taskList){
                String assignee = task.getAssignee();
                if(userId.equals(assignee)){
                    return task;
                }
            }
        }
        return taskList.get(0);
    }
}
