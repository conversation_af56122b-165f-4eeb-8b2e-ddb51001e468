package com.jeeplus.flsflow.domain;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 待办任务消息
 *
 * <AUTHOR>
 * @since 2025-01-03
 */
@Data
@Builder
public class TodoTaskMessage implements Serializable {

    private static final long serialVersionUID = 1;

    /**
     * 来源系统标识
     */
    private String sourceProjectName;

    /**
     * 来源系统地址
     */
    private String sourceProjectUrl;

    /**
     * 资源访问菜单路径
     */
    private String visitPath;

    /**
     * 资源访问参数
     */
    private String visitParam;

    /**
     * 小程序资源访问菜单路径
     */
    private String appletVisitPath;

    /**
     * 小程序资源访问参数
     */
    private String appletVisitParam;

    /**
     * 来源任务id
     */
    private String idSourceRecord;

    /**
     * 来源记录编码
     */
    private String sourceRecordCode;

    /**
     * 任务编号
     */
    private String taskCode;

    /**
     * 资源id
     */
    private String idResource;

    /**
     * 待办任务状态
     */
    private String taskStatus;

    /**
     * 待办任务名称
     */
    private String todoTaskName;

    /**
     * 代办人id
     */
    private String assignees;

    /**
     * 候选人列表
     */
    private String candidates;

    /**
     * 协办人列表
     */
    private String coOperators;

    /**
     * 分配类型，0：系统，1：指派，2：认领
     */
    private String assigneeType;

    /**
     * 经营主体id
     */
    private String idBizunit;

    /**
     * 发起人
     */
    private String initiator;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新人
     */
    private String updateBy;
}
