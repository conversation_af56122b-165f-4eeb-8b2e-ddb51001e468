package com.jeeplus.flsflow.enums;

public enum ApproveCodeEnum {
	/**
	 * 草稿
	 */
	DRAFT(0,"草稿"),
	
	/**
	 * 待审核
	 */
	SUBMITTED(1,"提交"),

	/**
	 * 已完成
	 */
	COMPLETED(2, "已完成"),

	/**
	 * 已撤回
	 */
	REVOKE(3, "撤回"),

	/**
	 * 驳回
	 */
	REJECTED(4, "驳回"),

	/**
	 * 作废
	 */
	VOIDED(5, "作废");

    private final int code;
    
    private final String name;

    ApproveCodeEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

	public String getName() {
		return name;
	}

	public int getCode() {
		return code;
	}
    
	public static ApproveCodeEnum getEnumByCode(Integer code){
        if (code == null){
            return null;
        }
        for (ApproveCodeEnum approveCodeEnum : ApproveCodeEnum.values()) {
            if (approveCodeEnum.getCode() == code.intValue()){
                return approveCodeEnum;
            }
        }
        return null;
    }
}
