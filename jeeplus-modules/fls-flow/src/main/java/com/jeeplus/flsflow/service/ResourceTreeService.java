package com.jeeplus.flsflow.service;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.jeeplus.fls.nc.api.domain.BaseResTrantype;
import com.jeeplus.fls.nc.api.domain.BaseResource;
import com.jeeplus.fls.nc.api.domain.BaseResourceClass;
import com.jeeplus.fls.nc.api.service.BaseResourceClassService;
import com.jeeplus.fls.nc.api.service.BaseResourceService;
import com.jeeplus.flsflow.domain.BillTreeNode;
import com.jeeplus.flsflow.enums.BillTreeNodeType;
import com.jeeplus.sys.constant.CommonConstants;

@Service
@DS("fls_db")
public class ResourceTreeService {
	
	@Autowired
	private BaseResourceClassService baseResourceClassService;

	@Autowired
	private BaseResourceService baseResourceService;

	public List<BillTreeNode> getTree(List<String> idResources) {
		List<BaseResourceClass> list = this.baseResourceClassService.lambdaQuery().eq(BaseResourceClass::getStatus, 2)
				.eq(BaseResourceClass::getDeleteFlag, CommonConstants.NO).eq(BaseResourceClass::getGrade, 1).list();
		List<BillTreeNode> nodeList = new ArrayList<BillTreeNode>();
		level1 : for (BaseResourceClass clazz : list) {
			BillTreeNode node = parseNode(clazz);
			// 查询分类下的资源
			List<BillTreeNode> childrenList = parseNodeList(this.baseResourceService.getResourcesByClassId(clazz.getIdResourceclass()), idResources);
			// 如果不是末级 说明有子目录
			if (CommonConstants.NO.equals(clazz.getEndFlag())) {
				// 查询子分类
				List<BaseResourceClass> subList = this.baseResourceClassService.lambdaQuery()
						.eq(BaseResourceClass::getStatus, 2).eq(BaseResourceClass::getDeleteFlag, CommonConstants.NO)
						.eq(BaseResourceClass::getIdParentclass, clazz.getIdResourceclass()).list();
				level2 : for (BaseResourceClass subclass : subList) {
					BillTreeNode subNode = parseNode(subclass);
					// 查询子分类下的资源
					List<BaseResource> resourceList = this.baseResourceService.getResourcesByClassId(subclass.getIdResourceclass());
					if(ObjectUtils.isEmpty(resourceList)) {
						continue level2;
					}
					List<BillTreeNode> childrenNodeList = parseNodeList(resourceList, idResources);
					if(ObjectUtils.isEmpty(childrenNodeList)) {
						continue level2;
					}
					subNode.setChildren(childrenNodeList);
					childrenList.add(subNode);
				}				
			}
			if(ObjectUtils.isEmpty(childrenList)) {
				continue level1;
			}
			node.setChildren(childrenList);
			nodeList.add(node);
		}
		return nodeList;
	}
	
	private List<BillTreeNode> parseNodeList(List<BaseResource> resourceList,List<String> idResources) {
		if(ObjectUtils.isEmpty(resourceList)) {
			return new ArrayList<BillTreeNode>();
		}
		List<BillTreeNode> nodeList = new ArrayList<BillTreeNode>();
		for(BaseResource baseResource : resourceList) {
			BillTreeNode nodeTmp = parseNode(baseResource);
			if(!idResources.contains(nodeTmp.getId())) {
				continue;
			}
			List<BaseResTrantype> tranTypeList = baseResource.getTrantypeList();
			if(ObjectUtils.isNotEmpty(tranTypeList)) {
				List<BillTreeNode> subList = new ArrayList<BillTreeNode>();
				for(BaseResTrantype trantype : tranTypeList) {
					subList.add(parseNode(trantype));
				}
				nodeTmp.setChildren(subList);
			}
			nodeList.add(nodeTmp);
		}
		return nodeList;
	}
	
	private BillTreeNode parseNode(BaseResourceClass baseResourceClass) {
		BillTreeNode node = new BillTreeNode();
		node.setName(baseResourceClass.getName());
		node.setId(baseResourceClass.getIdResourceclass());
		node.setVal(baseResourceClass.getCode());
		node.setType(BillTreeNodeType.CLAZZ.getType());
		return node;
	}
	
	private BillTreeNode parseNode(BaseResource baseResource) {
		BillTreeNode node = new BillTreeNode();
		node.setName(baseResource.getName());
		node.setId(baseResource.getIdResource());
		node.setVal(baseResource.getCode());
		node.setType(BillTreeNodeType.RESOURCE.getType());
		return node;
	}
	
	private BillTreeNode parseNode(BaseResTrantype baseResTrantype) {
		BillTreeNode node = new BillTreeNode();
		node.setName(baseResTrantype.getTranType());
		node.setId(baseResTrantype.getIdResTrantype());
		node.setVal(baseResTrantype.getTranCode());
		node.setType(BillTreeNodeType.TRANS.getType());
		return node;
	}
}
