package com.jeeplus.flsflow.utils;


import com.jeeplus.flsflow.enums.ErrorCodeEnum;
import com.jeeplus.flsflow.vo.BaseResult;

/**
 * <AUTHOR>
 * @date 2023/4/26 12:47
 */
public class ResultUtil {

    public static <T> BaseResult<T> ok() {
        return new BaseResult<>(ErrorCodeEnum.success.getKey(), ErrorCodeEnum.success.getValue(), null);
    }

    public static <T> BaseResult<T> ok(String msg) {
        return new BaseResult<>(ErrorCodeEnum.success.getKey(), msg, null);
    }

    public static <T> BaseResult<T> ok(String msg, T data) {
        return new BaseResult<>(ErrorCodeEnum.success.getKey(), msg, data);
    }

    public static <T> BaseResult<T> ok(T data) {
        return new BaseResult<>(ErrorCodeEnum.success.getKey(), ErrorCodeEnum.success.getValue(), data);
    }

    public static <T> BaseResult<T> error(String msg) {
        return new BaseResult<>(ErrorCodeEnum.biz_error.getKey(), msg, null);
    }

    public static <T> BaseResult<T> error(T data) {
        return new BaseResult<>(ErrorCodeEnum.biz_error.getKey(), ErrorCodeEnum.biz_error.getValue(), data);
    }

    public static <T> BaseResult<T> error(Integer code, String msg) {
        return new BaseResult<>(code, msg, null);
    }

    public static <T> BaseResult<T> error(Integer code, String msg, T data) {
        return new BaseResult<>(code, msg, data);
    }

    public static <T> BaseResult<T> error() {
        return new BaseResult<>(ErrorCodeEnum.biz_error.getKey(), ErrorCodeEnum.biz_error.getValue(), null);
    }

    public static <T> BaseResult<T> get(boolean b) {
        if (b) {
            return ResultUtil.ok();
        } else {
            return ResultUtil.error();
        }
    }


}
