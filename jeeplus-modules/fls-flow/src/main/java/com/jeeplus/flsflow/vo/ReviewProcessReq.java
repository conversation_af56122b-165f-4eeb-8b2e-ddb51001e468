package com.jeeplus.flsflow.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * created 2023/11/7 22:38:18
 */
@Data
public class ReviewProcessReq {
    @ApiModelProperty("流程实例id")
    @NotBlank
    private String processInstanceId;

    @ApiModelProperty("审批内容")
    @NotNull
    private ReviewReq reviewReq;

    @ApiModelProperty("流程表单入参,JSON格式")
    @NotNull
    private Object data;
}
