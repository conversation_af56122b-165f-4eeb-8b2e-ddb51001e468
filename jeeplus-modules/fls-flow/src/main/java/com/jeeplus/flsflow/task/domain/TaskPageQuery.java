package com.jeeplus.flsflow.task.domain;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.jeeplus.flsflow.task.enums.SourceType;
import com.jeeplus.flsflow.task.enums.SourceTypeConverter;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * created 2023/11/12 23:07:56
 */
@Data
public class TaskPageQuery {

    @ApiModelProperty("当前页")
    private long pageNo = 1;

    @ApiModelProperty("页大小")
    private long pageSize = 20;

    private String idApiTask;
    /**
     * 来源单据号
     */
    private String codeSource;

    /**
     * 来源单据类型
     */
    @JsonDeserialize(using = SourceTypeConverter.class)
    private SourceType sourceType;
}
