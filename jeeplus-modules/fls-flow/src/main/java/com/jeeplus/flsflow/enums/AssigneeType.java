package com.jeeplus.flsflow.enums;

/**
 * <AUTHOR>
 * created 2023/12/7 10:59:06
 */
public enum AssigneeType {
    USER("user"),
    POST("post"),
    COMPANY_OWNER("company"),
    DEPART_OWNER("depart"),
    ROLE("role"),
    ;

    final private String value;

    AssigneeType(String value){
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public static AssigneeType valueFrom(String value){
        for (AssigneeType assigneeType : AssigneeType.values()) {
            if (assigneeType.getValue().equalsIgnoreCase(value)){
                return assigneeType;
            }
        }
        throw new RuntimeException("AssigneeType类型匹配失败");
    }
}
