package com.jeeplus.flsflow.task.service.impl;


import com.jeeplus.flsflow.task.domain.TaskResult;
import com.jeeplus.flsflow.task.service.TaskResultParser;
import org.springframework.util.ObjectUtils;

/**
 * <AUTHOR>
 * created 2023/11/25 09:54:21
 */
public class DefaultTaskResultParser extends TaskResultParser {
    @Override
    public TaskResult parse(String result) {
        TaskResult taskResult = new TaskResult();
        if (!ObjectUtils.isEmpty(result) && "ok".equalsIgnoreCase(result)){
            taskResult.setSuc(true);
            taskResult.setResult(result);
            taskResult.setRemark("执行成功");
        }else {
            taskResult.setSuc(false);
            taskResult.setResult(result);
        }
        return taskResult;
    }
}
