package com.jeeplus.flsflow.listener;

import org.flowable.common.engine.api.delegate.event.FlowableEngineEventType;
import org.flowable.common.engine.api.delegate.event.FlowableEntityEvent;
import org.flowable.common.engine.api.delegate.event.FlowableEvent;
import org.flowable.common.engine.api.delegate.event.FlowableEventListener;
import org.flowable.task.service.impl.persistence.entity.TaskEntity;
import org.springframework.stereotype.Component;

/**
 * 全局任务监听
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Component
public class GlobalTaskAssignmentListener implements FlowableEventListener {

    @Override
    public void onEvent(FlowableEvent event) {
        if (event instanceof FlowableEntityEvent) {
            FlowableEntityEvent entityEvent = (FlowableEntityEvent) event;
            if (entityEvent.getEntity() instanceof TaskEntity) {
                TaskEntity task = (TaskEntity) entityEvent.getEntity();
                if (event.getType() == FlowableEngineEventType.TASK_CREATED) {

                }
            }
        }
    }

    @Override
    public boolean isFailOnException() {
        return false;
    }

    @Override
    public boolean isFireOnTransactionLifecycleEvent() {
        return false;
    }

    @Override
    public String getOnTransaction() {
        return null;
    }
}
