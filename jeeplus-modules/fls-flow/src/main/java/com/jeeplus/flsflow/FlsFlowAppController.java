package com.jeeplus.flsflow;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson2.JSON;
import com.jeeplus.aop.logging.annotation.ApiLog;
import com.jeeplus.flowable.controller.FlowableFormController;
import com.jeeplus.flowable.controller.FlowableTaskController;
import com.jeeplus.flowable.model.Flow;
import com.jeeplus.flowable.model.TaskComment;
import com.jeeplus.flowable.service.FlowTaskService;
import com.jeeplus.flowable.service.FlowableBpmnModelService;
import com.jeeplus.flsflow.domain.WorkflowModel;
import com.jeeplus.flsflow.enums.ReviewType;
import com.jeeplus.flsflow.service.FlsFlowService;
import com.jeeplus.flsflow.service.WorkflowInstanceService;
import com.jeeplus.flsflow.service.WorkflowModelService;
import com.jeeplus.flsflow.utils.ResultUtil;
import com.jeeplus.flsflow.vo.*;
import com.jeeplus.sys.utils.UserUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.flowable.engine.FormService;
import org.flowable.engine.TaskService;
import org.flowable.engine.form.FormProperty;
import org.flowable.engine.form.TaskFormData;
import org.flowable.task.api.Task;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * created 2023/11/8 17:13:28
 */
@Api(tags = "流程应用")
@RestController
public class FlsFlowAppController {

    @Resource
    private FlowableFormController flowableFormController;

    @Resource
    private FlsFlowService flsFlowService;

    @Resource
    private WorkflowInstanceService workflowInstanceService;

    @Resource
    private WorkflowModelService workflowModelService;

    @Resource
    private FlowableTaskController flowableTaskController;

    @Resource
    private FlowTaskService flowTaskService;

    @Autowired
    private FormService formService;
    @Autowired
    private TaskService taskService;

    @Resource
    private FlowableBpmnModelService flowableBpmnModelService;

    @ApiLog("发起流程")
    @ApiOperation("发起流程")
    @PostMapping("/workflow/startProcess")
    public BaseResult<String> startProcess(@RequestBody @Valid StartProcessReq req) {
        return ResultUtil.ok("", workflowInstanceService.saveInstance(req));
    }

    @ApiLog("查询当前流程审批节点表单")
    @ApiOperation("查询当前流程审批节点表单")
    @GetMapping("/workflow/reviewFrom")
    public BaseResult<List<FormProperty>> processFrom(String processInsId) {
        Task currentTask = taskService.createTaskQuery().processInstanceId(processInsId).list().get(0);
        TaskFormData taskFormData = formService.getTaskFormData(currentTask.getId());//根据任务ID拿取表单数据
        return ResultUtil.ok(taskFormData.getFormProperties());
    }


    @ApiLog("查询当前流程审批节点")
    @ApiOperation("查询当前审批节点")
    @GetMapping("/workflow/reviewTask")
    public BaseResult<Task> processTask(String processInsId) {
        Task currentTask = taskService.createTaskQuery().processInstanceId(processInsId).list().get(0);
        return ResultUtil.ok(currentTask);
    }

    @ApiLog("通过业务条件发起流程")
    @ApiOperation("通过业务条件发起流程")
    @PostMapping("/workflow/startProcessByCondition")
    public BaseResult<String> startProcess(@RequestBody @Valid StartProcessByConditionReq req) {
        WorkflowModel workflowModel = workflowModelService.routeByCondition(req.getRoute());
        if (ObjectUtils.isEmpty(workflowModel)) {
            throw new RuntimeException("工作流不存在");
        }
        StartProcessReq startProcessReq = BeanUtil.copyProperties(req, StartProcessReq.class);
        startProcessReq.setWorkflowId(workflowModel.getId());
        return ResultUtil.ok("", workflowInstanceService.saveInstance(startProcessReq));
    }

    @ApiOperation("通过业务条件查询流程配置")
    @PostMapping("/workflow/processByCondition")
    public BaseResult<WorkflowModel> processByCondition(@RequestBody @Valid RouteReq route) {
        return ResultUtil.ok("", workflowModelService.routeByCondition(route));
    }

    @ApiLog("流程转办")
    @ApiOperation("转办")
    @PostMapping("/workflow/transferTask")
    public BaseResult<String> transferTask(@RequestBody @Valid TransferReq transferReq){
        //查询任务
        String userId = UserUtils.getCurrentUserDTO().getId();
        Task task = flsFlowService.queryTaskByProcessInsId(transferReq.getProcessInstanceId(), userId);
        if (ObjectUtils.isEmpty(task)) {
            throw new RuntimeException("当前用户,任务不存在");
        }
        flowableTaskController.transferTask(task.getId(), transferReq.getUserId());
        return ResultUtil.ok("转派成功");
    }

    @ApiLog("流程审批")
    @ApiOperation("流程审批")
    @PostMapping("/workflow/reviewProcess")
    public BaseResult<Object> reviewProcess(@RequestBody @Valid ReviewProcessReq req) {
        //查询任务
        String userId = UserUtils.getCurrentUserDTO().getId();
        Task task = flsFlowService.queryTaskByProcessInsId(req.getProcessInstanceId(), userId);
        if (ObjectUtils.isEmpty(task)) {
            throw new RuntimeException("当前用户,任务不存在");
        }
        Flow flow = new Flow();
        flow.setProcInsId(req.getProcessInstanceId());
        flow.setTaskId(task.getId());
        flow.setComment(new TaskComment());
        ReviewType reviewType = ReviewType.valueOf(req.getReviewReq().getReviewType());
        if (reviewType.toActionType() == null) {
            throw new RuntimeException("");
        }
        flow.getComment().setMessage(req.getReviewReq().getRemark());
        flow.getComment().setCommentType(TaskComment.prefix + reviewType.toActionType().getType());
        flow.getComment().setStatus(reviewType.toActionType().getStatus());

        ResponseEntity response = ResponseEntity.ok("未定义操作");
        switch (reviewType) {
            case AGREE:
                response = flowableFormController.submitTaskFormData(flow, JSON.toJSONString(req.getData()));
                break;
            case REJECT:
                List<Flow> nodes = flowTaskService.getBackNodes(task.getId());
                if (ObjectUtils.isEmpty(nodes)) {
                    throw new RuntimeException("没有可以驳回的节点");
                }
                Flow backFlow = nodes.get(nodes.size() - 1);
                response = flowableTaskController.back(backFlow.getTaskDefKey(), task.getId(), flow.getComment());
                break;
        }
        return ResultUtil.ok(response.getBody());
    }


    @GetMapping("/workflow/historicTaskList")
    @ApiOperation("流程审批历史任务")
    public BaseResult<FlowVo> historicTaskList(@ApiParam(value = "审批实例id", required = true) String processInsId) throws Exception {
        if (ObjectUtils.isEmpty(processInsId)) {
            return ResultUtil.error("processInsId不能为空");
        }
        List<Flow> historicTaskList = flowTaskService.historicTaskList(processInsId);
        FlowVo flowVo = new FlowVo();
        flowVo.setHistoryFlowList(historicTaskList);
        List<FlowNodeVo> flowNodeVos = new ArrayList<>(0);
        for (Flow flow : historicTaskList) {
            Integer replaceIdx = null;
            for (FlowNodeVo flowNodeVo : flowNodeVos) {
                if (flowNodeVo.getNodeName().equals(flow.getHistIns().getActivityName())){
                    replaceIdx = flowNodeVos.indexOf(flowNodeVo);
                }
            }
            FlowNodeVo flowNodeVo = new FlowNodeVo();
            flowNodeVo.setNodeName(flow.getHistIns().getActivityName());
            flowNodeVo.setCreateTime(flow.getHistIns().getTime());
            flowNodeVo.setAssigneeName(flow.getAssigneeName());
            if ( null != replaceIdx) {
                flowNodeVos.set(replaceIdx, flowNodeVo);
            }else {
                flowNodeVos.add(flowNodeVo);
            }
        }
        flowVo.setFlowNodeList(flowNodeVos);
        return ResultUtil.ok(flowVo);
    }
}
