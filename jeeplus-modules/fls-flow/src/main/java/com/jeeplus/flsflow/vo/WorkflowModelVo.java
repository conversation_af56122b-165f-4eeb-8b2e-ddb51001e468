package com.jeeplus.flsflow.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * created 2023/11/10 16:57:20
 */
@Data
@ApiModel
public class WorkflowModelVo {
    private String id;
    /**
     *  varchar(50) NOT NULL COMMENT '流程模型key',
     */
    @ApiModelProperty("流程模型key")
    private String modelKey;
    @ApiModelProperty("流程模型id")
    private String modelId;

    @ApiModelProperty("1-启用,2-禁用")
    private Integer status;

    /**
     *  varchar(36) NOT NULL COMMENT '流程名称',
     */
    private String name;
    /**
     *  varchar(50) NOT NULL COMMENT '业务类型:purchase_review外采,repair_log维修日志',
     */
    private Integer conditionType;

    /**
     * 流程条件
     */
    private List<String> conditionValueList;
    private List<String> conditionNameList;
}
