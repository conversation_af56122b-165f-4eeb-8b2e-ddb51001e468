package com.jeeplus.flsflow.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * created 2023/11/7 22:38:18
 */
@Data
public class StartProcessByConditionReq {
    @ApiModelProperty("业务路由")
    @NotNull
    @Valid
    private RouteReq route;

    @ApiModelProperty("流程标题, *在*时间发起了*流程")
    private String title;

    @ApiModelProperty(value = "流程表单入参,JSON格式", required = true)
    @NotNull
    private Object data;

    @ApiModelProperty("审批过程监听,回调地址")
    private String listenerUrl;
}
