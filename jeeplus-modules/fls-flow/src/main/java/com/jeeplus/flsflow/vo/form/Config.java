package com.jeeplus.flsflow.vo.form;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;

import lombok.Data;

@Data
public class Config {
	private int labelWidth = 100;
	private String labelPosition = "right";
	private String size = "default";
	private String customClass = "";
	private String ui = "element";
	private String layout = "horizontal";
	private String width = "100%";
	private boolean hideLabel = false;
	private boolean hideErrorMessage = false;
	private JSONArray eventScript = intEventScript();
	private JSONArray dataSource = new JSONArray();
	
	private Config() {
		
	}
	
	private JSONArray intEventScript() {
		JSONArray array = new JSONArray();
		JSONObject obj1 = new JSONObject();
		obj1.put("key", "mounted");
		obj1.put("name", "mounted");
		obj1.put("func", "");
		array.add(obj1);
		JSONObject obj2 = new JSONObject();
		obj2.put("key", "refresh");
		obj2.put("name", "refresh");
		obj2.put("func", "");
		array.add(obj2);
		return array;
	}
	
	public static Config build() {
		return new Config();
	}
}
