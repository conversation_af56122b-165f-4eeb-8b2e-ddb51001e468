package com.jeeplus.flsflow.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jeeplus.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * created 2023/11/10 15:01:48
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("t_workflow_instance")
public class WorkflowInstance extends BaseEntity {
    /**
     *  varchar(36) NOT NULL COMMENT '流程模型id',
     */
    private String flowableModelId;
    /**
     *  varchar(50) NOT NULL COMMENT '流程模型key',
     */
    private String flowableModelKey;

    /**
     *  varchar(36) NOT NULL COMMENT '实例id',
     */
    private String flowableInsId;

    /**
     * 监听回调地址-post content-type:json
     */
    private String listenerUrl;
}
