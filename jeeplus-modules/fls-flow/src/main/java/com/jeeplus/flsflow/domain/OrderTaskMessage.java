package com.jeeplus.flsflow.domain;

import lombok.Builder;
import lombok.Data;

/**
 * 工单任务消息
 *
 * <AUTHOR>
 * @since 2025-01-03
 */
@Data
@Builder
public class OrderTaskMessage {
    /**
     * 事件名称
     */
    private String eventName;
    /**
     * 办理人id列表
     */
    private String assignees;
    /**
     * 候选人id列表
     */
    private String candidates;
    /**
     * 流程实例id
     */
    private String idProcInst;
    /**
     * 流程任务id
     */
    private String idProcTask;
    /**
     * 工单任务定义id
     */
    private String idTaskDefinition;
    /**
     * 工单记录id
     */
    private String idWorkorderRecord;
    /**
     * 工单任务备注
     */
    private String remarks;
}
