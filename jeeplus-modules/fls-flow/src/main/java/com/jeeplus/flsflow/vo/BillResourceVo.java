package com.jeeplus.flsflow.vo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 单据资源注册VO
 * <AUTHOR>
 *
 */
@Data
public class BillResourceVo {
	
	@ApiModelProperty("资源ID")
    @NotBlank(message = "资源ID不能为空")
	private String idResource;
	
	@ApiModelProperty("单据编码")
    @NotBlank(message = "单据编码不能为空")
	private String billCode;

	@ApiModelProperty("单据名称")
    @NotBlank(message = "单据名称不能为空")
	private String billName;

	@ApiModelProperty("单据元数据")
    @NotNull(message = "单据元数据不能为空")
	private JSONObject metadata;

	@ApiModelProperty("表单数据")
	@NotEmpty(message = "表单数据不能为空")
	private JSONArray formdata;
	
	@ApiModelProperty("默认限制类型")
	private String defaultLimitType;
	
	@ApiModelProperty("默认限定的单据字段KEY")
	private String defaultLimitVal;
}
