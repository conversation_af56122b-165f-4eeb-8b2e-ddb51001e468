package com.jeeplus.flsflow.task.domain;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.jeeplus.flsflow.task.enums.SourceType;
import com.jeeplus.flsflow.task.enums.SourceTypeConverter;
import lombok.Data;

/**
 * <AUTHOR>
 * created 2023/11/12 23:07:56
 */
@Data
public class TaskQuery {

    private String idApiTask;
    /**
     * 来源单据号
     */
    private String codeSource;

    /**
     * 来源单据类型
     */
    @JsonDeserialize(using = SourceTypeConverter.class)
    private SourceType sourceType;
}
