package com.jeeplus.flsflow.task.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jeeplus.flsflow.task.domain.ApiTaskReq;
import com.jeeplus.flsflow.task.domain.TaskPageQuery;
import com.jeeplus.flsflow.task.domain.TaskQuery;
import com.jeeplus.flsflow.task.domain.TaskResult;
import com.jeeplus.flsflow.task.domain.entity.ApiTask;
import com.jeeplus.flsflow.task.enums.TaskMethod;
import com.jeeplus.flsflow.task.enums.TaskStatus;
import com.jeeplus.flsflow.task.http.Request;
import com.jeeplus.flsflow.task.http.RestHttpTemplate;
import com.jeeplus.flsflow.task.mapper.TaskMapper;
import com.jeeplus.flsflow.task.service.ArgBuilder;
import com.jeeplus.flsflow.task.service.TaskResultParser;
import com.jeeplus.flsflow.task.service.TaskService;
import com.jeeplus.security.util.UserSessionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.concurrent.Future;

/**
 * <AUTHOR>
 * created 2023/11/12 10:47:55
 */
@Service
@Slf4j
public class TaskServiceImpl extends ServiceImpl<TaskMapper, ApiTask> implements TaskService {

    @Resource
    private RestHttpTemplate restHttpTemplate;

    @Override
    public String addTask(ApiTaskReq apiTaskReq, String arguments) {
        if (ObjectUtils.isEmpty(apiTaskReq.getIdSource()) ||
                ObjectUtils.isEmpty(apiTaskReq.getSourceType()) ||
                ObjectUtils.isEmpty(apiTaskReq.getCodeSource())) {
            throw new RuntimeException("来源信息不能为空");
        }
        ApiTask oldApiTask = this.lambdaQuery().eq(ApiTask::getIdSource, apiTaskReq.getIdSource())
                .eq(ApiTask::getSourceType, apiTaskReq.getSourceType())
                .eq(ApiTask::getDeleteFlag, "0")
                .one();
        if (!ObjectUtils.isEmpty(oldApiTask)) {
            //已存在，删除老数据
            this.lambdaUpdate()
                    .set(ApiTask::getDeleteFlag, "1")
                    .eq(ApiTask::getIdApiTask, oldApiTask.getIdApiTask())
                    .update();
        }
        //任务入库
        ApiTask task = BeanUtil.copyProperties(apiTaskReq, ApiTask.class);
        task.setRequestData(arguments);
        buildNormalField(task);
        this.save(task);
        return task.getIdApiTask();
    }

    @Override
    public ApiTask addTask(ApiTaskReq apiTaskReq, ArgBuilder delegate) {
        ApiTask task = BeanUtil.copyProperties(apiTaskReq, ApiTask.class);
        task.setArgBuilder(delegate.getClass().getName());
        buildNormalField(task);
        this.save(task);
        return task;
    }

    private void buildNormalField(ApiTask task) {
        task.setCreator(UserSessionUtils.getCurrent().getIdUser());
        task.setCreateTime(LocalDateTime.now());
        task.setResponseData("");
    }

    @Override
    public ApiTask target(TaskQuery taskQuery, TaskResultParser parser) {
        ApiTask apiTask = queryOneTask(taskQuery);
        if (ObjectUtils.isEmpty(apiTask)) {
            throw new RuntimeException("任务不存在");
        }
        Request request = new Request();
        request.setUrl(apiTask.getRequestUrl());
        String arguments;
        if (!ObjectUtils.isEmpty(apiTask.getArgBuilder())) {
            try {
                Class<?> cls = Class.forName(apiTask.getArgBuilder());
                ArgBuilder delegate = (ArgBuilder) cls.newInstance();
                arguments = delegate.buildArguments(apiTask);
            } catch (Exception e) {
                log.error("入参代理类执行异常", e);
                throw new RuntimeException("入参代理类执行异常");
            }
        } else {
            arguments = apiTask.getRequestData();
        }
        request.setArgs(arguments);
        String result = "";
        try {
            apiTask.setRequestData(arguments);
            apiTask.setExecuteCount(apiTask.getExecuteCount() + 1);
            apiTask.setLastExecuteTime(LocalDateTime.now());
            switch (TaskMethod.valueOf(apiTask.getMethod())) {
                case GET:
                    result = restHttpTemplate.get(request);
                    break;
                case POST:
                    result = restHttpTemplate.post(request);
                    break;
                default:
                    throw new RuntimeException("不支持的http方法");
            }
            if ( parser == null){
                parser = new DefaultTaskResultParser();
            }
            TaskResult taskResult = parser.parse(result);
            apiTask.setResponseData(taskResult.getResult());
            if (taskResult.getSuc()){
                apiTask.setTaskStatus(TaskStatus.SUCCESS.toString());
            }else {
                apiTask.setTaskStatus(TaskStatus.FAIL.toString());
            }
            apiTask.setResponseRemark(taskResult.getRemark());
        } catch (Exception e) {
            log.error("api任务执行异常", e);
            log.error("api任务执行异常" + result);
            apiTask.setTaskStatus(TaskStatus.FAIL.toString());
            apiTask.setResponseData(e.getClass().getName());
        }
        this.updateById(apiTask);
        return apiTask;
    }

    @Override
    @Async(value = "apiTaskAsync")
    public Future<ApiTask> asyncTarget(TaskQuery taskQuery, TaskResultParser parser) {
        ApiTask apiTask = target(taskQuery, parser);
        return new AsyncResult<>(apiTask);
    }

    @Override
    public Page<ApiTask> query(TaskPageQuery pageQuery) {
        Page<ApiTask> apiTaskPage = new Page<>(pageQuery.getPageNo(), pageQuery.getPageSize());
        return this.lambdaQuery()
                .eq(!ObjectUtils.isEmpty(pageQuery.getIdApiTask()), ApiTask::getIdApiTask, pageQuery.getIdApiTask())
                .eq(!ObjectUtils.isEmpty(pageQuery.getSourceType()), ApiTask::getSourceType, pageQuery.getSourceType())
                .eq(!ObjectUtils.isEmpty(pageQuery.getCodeSource()), ApiTask::getCodeSource, pageQuery.getCodeSource())
                .eq(ApiTask::getDeleteFlag, "0")
                .page(apiTaskPage);
    }

    @Override
    public ApiTask queryOneTask(TaskQuery taskQuery) {
        if (ObjectUtils.isEmpty(taskQuery.getIdApiTask()) &&
                (ObjectUtils.isEmpty(taskQuery.getCodeSource()) || ObjectUtils.isEmpty(taskQuery.getSourceType())))
        {
            throw new RuntimeException("查询参数不能为空");
        }
        return this.lambdaQuery()
                .eq(!ObjectUtils.isEmpty(taskQuery.getIdApiTask()), ApiTask::getIdApiTask, taskQuery.getIdApiTask())
                .eq(!ObjectUtils.isEmpty(taskQuery.getSourceType()), ApiTask::getSourceType, taskQuery.getSourceType())
                .eq(!ObjectUtils.isEmpty(taskQuery.getCodeSource()), ApiTask::getCodeSource, taskQuery.getCodeSource())
                .eq(ApiTask::getDeleteFlag, "0")
                .one();

    }
}
