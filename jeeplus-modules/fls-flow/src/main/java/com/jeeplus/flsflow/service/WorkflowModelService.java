package com.jeeplus.flsflow.service;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jeeplus.common.utils.TransactionTemplateHelper;
import com.jeeplus.core.domain.BaseEntity;
import com.jeeplus.flowable.service.FlowProcessService;
import com.jeeplus.flowable.service.FlowableModelService;
import com.jeeplus.flowable.vo.ProcessVo;
import com.jeeplus.fls.nc.api.domain.BaseOrg;
import com.jeeplus.fls.nc.api.service.BaseOrgService;
import com.jeeplus.flsflow.domain.WorkflowCondition;
import com.jeeplus.flsflow.domain.WorkflowModel;
import com.jeeplus.flsflow.enums.WorkflowConditionType;
import com.jeeplus.flsflow.exception.WorkflowException;
import com.jeeplus.flsflow.mapper.WorkflowModelMapper;
import com.jeeplus.flsflow.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.repository.ProcessDefinition;
import org.flowable.ui.modeler.domain.Model;
import org.flowable.ui.modeler.serviceapi.ModelService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallbackWithoutResult;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * created 2023/11/10 15:06:27
 */
@Service
@Slf4j
public class WorkflowModelService extends ServiceImpl<WorkflowModelMapper, WorkflowModel> {

    @Resource
    private WorkflowConditionService workflowConditionService;

    @Resource
    private ModelService modelService;

    @Resource
    private FlowProcessService flowProcessService;

    @Resource
    private FlowableModelService flowableModelService;

    @Resource
    private BaseOrgService baseOrgService;

    @Resource
    private TransactionTemplateHelper transactionTemplateHelper;

    @Resource
    private WorkflowModelMapper workflowModelMapper;

    public WorkflowModel create(WorkflowCreateReq createReq) {
        WorkflowConditionType conditionType = WorkflowConditionType.fromValue(createReq.getConditionType());

        Map<String, String> bizInfoMap = queryBizInfo(conditionType, createReq.getBizIdList());
        WorkflowModelService self = this;
        return transactionTemplateHelper.execute(status -> {
            WorkflowModel workflowModel = new WorkflowModel();
            workflowModel.setName(createReq.getName());
            workflowModel.setStatus(createReq.getStatus());
            workflowModel.setWorkflowType(createReq.getWorkflowType());
            self.save(workflowModel);
            List<WorkflowCondition> conditionList = createReq.getBizIdList().stream().map(it -> {
                WorkflowCondition workflowCondition = new WorkflowCondition();
                workflowCondition.setBizId(it);
                workflowCondition.setBizName(bizInfoMap.get(it));
                workflowCondition.setConditionType(conditionType.getValue());
                workflowCondition.setWorkflowModelId(workflowModel.getId());
                return workflowCondition;
            }).collect(Collectors.toList());
            workflowConditionService.saveBatch(conditionList);
            return workflowModel;
        });
    }

    public Map<String, String> queryBizInfo(WorkflowConditionType conditionType, Set<String> idList) {
        if (Objects.requireNonNull(conditionType) == WorkflowConditionType.ORG) {
            try {
                DynamicDataSourceContextHolder.push("fls_db");
                List<BaseOrg> bizunitList = baseOrgService.getOrgList(idList);
                return bizunitList.stream().collect(Collectors.toMap(BaseOrg::getIdOrg, it->"["+it.getCode()+"]"+it.getName()));
            } finally {
                DynamicDataSourceContextHolder.poll();
            }
        }
        return new HashMap<>(0);
    }

    public boolean update(WorkflowUpdateReq updateReq) {
        WorkflowConditionType conditionType = WorkflowConditionType.fromValue(updateReq.getConditionType());
        Map<String, String> bizNameMap = queryBizInfo(conditionType, updateReq.getBizIdList());
        WorkflowModelService self = this;
        WorkflowModel workflowModel = this.getById(updateReq.getId());
        if (1 == updateReq.getStatus()) {
            checkExistWorkflow(workflowModel, conditionType, updateReq.getBizIdList());
        }
        return transactionTemplateHelper.execute(status -> {
            if (!workflowModel.getName().equals(updateReq.getName())) {
                self.lambdaUpdate()
                        .set(WorkflowModel::getName, updateReq.getName())
                        .eq(BaseEntity::getId, updateReq.getId())
                        .update();
            }
            if (!workflowModel.getStatus().equals(updateReq.getStatus())) {
                self.lambdaUpdate()
                        .set(WorkflowModel::getStatus, updateReq.getStatus())
                        .eq(BaseEntity::getId, updateReq.getId())
                        .update();
            }
            List<WorkflowCondition> workflowConditionList = workflowConditionService.lambdaQuery()
                    .eq(WorkflowCondition::getWorkflowModelId, updateReq.getId())
                    .list();
            Set<String> oldBizIdList = workflowConditionList.stream().map(WorkflowCondition::getBizId).collect(Collectors.toSet());
            if (oldBizIdList.containsAll(updateReq.getBizIdList()) &&
                    updateReq.getBizIdList().containsAll(oldBizIdList)) {
                //无变更
                return true;
            }

            workflowConditionService.removeByIds(workflowConditionList.stream().map(BaseEntity::getId).collect(Collectors.toList()));

            List<WorkflowCondition> conditionList = updateReq.getBizIdList().stream().map(it -> {
                WorkflowCondition workflowCondition = new WorkflowCondition();
                workflowCondition.setBizId(it);
                workflowCondition.setBizName(bizNameMap.get(it));
                workflowCondition.setConditionType(conditionType.getValue());
                workflowCondition.setWorkflowModelId(workflowModel.getId());
                return workflowCondition;
            }).collect(Collectors.toList());
            workflowConditionService.saveBatch(conditionList);
            return true;
        });
    }

    private void checkExistWorkflow(WorkflowModel workflowModel, WorkflowConditionType conditionType, Set<String> bizIds)
    {
        //如果是启用，需要判断是否有重复启用的配置
        RouteReq routeReq = new RouteReq();
        routeReq.setWorkflowType(workflowModel.getWorkflowType());
        routeReq.setConditionType(conditionType.getValue());
        for (String bizId : bizIds) {
            routeReq.setBizId(bizId);
            WorkflowModel existModel = routeByCondition(routeReq);
            if (!ObjectUtils.isEmpty(existModel)){
                if (existModel.getId().equals(workflowModel.getId())){
                    continue;
                }
                throw new WorkflowException(existModel.getName() + ",已存在启用状态流程配置");
            }
        }
    }
    public Page<WorkflowModelVo> list(WorkflowPageReq req) {
        IPage<WorkflowModel> workflowModelPage = this.lambdaQuery()
                .like(!ObjectUtils.isEmpty(req.getKeyword()), WorkflowModel::getName, req.getKeyword())
                .eq(!ObjectUtils.isEmpty(req.getBizType()), WorkflowModel::getWorkflowType, req.getBizType())
                .orderByDesc(BaseEntity::getCreateTime)
                .page(new Page<>(req.getCurrent(), req.getSize()));

        Page<WorkflowModelVo> modelVoPage = new Page<>(workflowModelPage.getCurrent(), workflowModelPage.getSize(), workflowModelPage.getTotal());
        if (ObjectUtils.isEmpty(workflowModelPage.getRecords())) {
            return modelVoPage;
        }

        List<String> workflowIdList = workflowModelPage.getRecords()
                .stream()
                .map(WorkflowModel::getId)
                .collect(Collectors.toList());

        List<WorkflowCondition> conditionList = workflowConditionService.lambdaQuery()
                .in(WorkflowCondition::getWorkflowModelId, workflowIdList)
                .list();

        Map<String, List<WorkflowCondition>> conditionGroup = conditionList.stream()
                .collect(Collectors.groupingBy(WorkflowCondition::getWorkflowModelId));

        List<WorkflowModelVo> modelVoList = workflowModelPage.getRecords().stream().map(it -> {
            WorkflowModelVo modelVo = BeanUtil.copyProperties(it, WorkflowModelVo.class);
            modelVo.setConditionType(conditionGroup.get(it.getId()).get(0).getConditionType());

            modelVo.setConditionNameList(conditionGroup.get(it.getId()).stream()
                    .map(WorkflowCondition::getBizName)
                    .distinct()
                    .collect(Collectors.toList()));

            modelVo.setConditionValueList(conditionGroup.get(it.getId()).stream()
                    .map(WorkflowCondition::getBizId)
                    .distinct()
                    .collect(Collectors.toList()));

            modelVo.setModelKey(it.getFlowableModelKey());
            modelVo.setModelId(it.getFlowableModelId());
            return modelVo;
        }).collect(Collectors.toList());

        return modelVoPage.setRecords(modelVoList);
    }

    public void unbind(IdReq idReq) {
        this.lambdaUpdate()
                .set(WorkflowModel::getFlowableModelId, "")
                .set(WorkflowModel::getFlowableModelKey, "")
                .set(WorkflowModel::getStatus, 2)
                .eq(BaseEntity::getId, idReq.getId())
                .update();
    }

    public WorkflowModel delete(IdReq idReq) {
        WorkflowModel workflowModel = this.getById(idReq.getId());
        if (ObjectUtils.isEmpty(workflowModel)) {
            throw new WorkflowException("工作流不存在");
        }
        //是否存在流程中的实例
        ProcessDefinition processDefinition = flowProcessService.getProcessDefinitionByKey(workflowModel.getFlowableModelKey());
        if (!ObjectUtils.isEmpty(processDefinition)) {
            try {
                Page<ProcessVo> result = flowProcessService.runningList(new Page<>(1, 1), null, null, processDefinition.getKey());
                if (!ObjectUtils.isEmpty(result.getRecords())) {
                    throw new WorkflowException("存在进行中的工作流");
                }
            } catch (Exception e) {
                log.error("查询流程实例异常", e);
                throw new WorkflowException("查询流程实例异常");
            }
        }

        flowableModelService.delete(workflowModel.getFlowableModelId());

        WorkflowModelService self = this;
        transactionTemplateHelper.execute(new TransactionCallbackWithoutResult() {
            @Override
            protected void doInTransactionWithoutResult(TransactionStatus status) {
                self.removeById(workflowModel.getId());
                workflowConditionService.lambdaUpdate()
                        .set(BaseEntity::getDelFlag, 1)
                        .eq(WorkflowCondition::getWorkflowModelId, workflowModel.getId())
                        .update(new WorkflowCondition());
            }
        });
        return null;
    }

    public WorkflowModel routeByCondition(RouteReq req) {
        return workflowModelMapper.routWorkflow(req);
    }

    public WorkflowModel bindFlowableModelId(String flowableModelId, String workflowModelId) {
        WorkflowModel workflowModel = this.getById(workflowModelId);
        if (ObjectUtils.isEmpty(workflowModel)) {
            throw new WorkflowException("流程不存在");
        }
        Model model = this.modelService.getModel(flowableModelId);
        this.lambdaUpdate().set(WorkflowModel::getFlowableModelId, model.getId())
                .set(WorkflowModel::getFlowableModelKey, model.getKey())
                .eq(BaseEntity::getId, workflowModelId)
                .update();
        return this.getById(workflowModelId);
    }
}
