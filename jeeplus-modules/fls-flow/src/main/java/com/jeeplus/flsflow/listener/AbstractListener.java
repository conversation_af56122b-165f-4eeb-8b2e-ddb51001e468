package com.jeeplus.flsflow.listener;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson2.JSON;
import com.jeeplus.flsflow.domain.TaskEvent;
import com.jeeplus.flsflow.domain.WorkflowInstance;
import com.jeeplus.flsflow.service.WorkflowInstanceService;
import com.jeeplus.flsflow.task.domain.ApiTaskReq;
import com.jeeplus.flsflow.task.domain.TaskQuery;
import com.jeeplus.flsflow.task.enums.SourceType;
import com.jeeplus.flsflow.task.enums.TaskMethod;
import com.jeeplus.flsflow.task.service.TaskService;
import com.jeeplus.security.util.UserSessionUtils;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.ObjectUtils;

/**
 * <AUTHOR>
 * created 2023/11/30 18:39:56
 */
public abstract class AbstractListener {
    protected void doTaskCallback(TaskEvent taskEvent){
        //获取回调地址
        WorkflowInstanceService instanceService = SpringUtil.getBean(WorkflowInstanceService.class);
        WorkflowInstance workflowInstance = instanceService.lambdaQuery()
                .eq(WorkflowInstance::getFlowableInsId, taskEvent.getProcessInsId())
                .one();
        if (ObjectUtils.isEmpty(workflowInstance)){
            //发起流程，第一个任务创建不能回调，实例还未入库
            SpringUtil.getBean(FlsTaskProxyListener.class).manualNotify(taskEvent, UserSessionUtils.getCurrent(), true);
            return;
        }
        if (ObjectUtils.isEmpty(workflowInstance.getListenerUrl())){
            return;
        }
        doTaskCallback(taskEvent, workflowInstance);
    }

    protected void doTaskCallback(TaskEvent taskEvent, WorkflowInstance workflowInstance){
        com.jeeplus.flsflow.task.service.TaskService taskService = SpringUtil.getBean(TaskService.class);
        ApiTaskReq req = new ApiTaskReq();
        req.setMethod(TaskMethod.POST);
        req.setRequestUrl(workflowInstance.getListenerUrl());
        req.setIdSource(taskEvent.getTaskId());
        req.setCodeSource(taskEvent.getProcessInsId());
        req.setSourceType(SourceType.PROCESS_TASK_INSTANCE+"_"+taskEvent.getEvent().toUpperCase());
        String idTask = taskService.addTask(req, JSON.toJSONString(taskEvent));
//        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
//            @Override
//            public void afterCommit() {
                TaskQuery taskQuery = new TaskQuery();
                taskQuery.setIdApiTask(idTask);
                taskService.target(taskQuery, null);
//            }
//        });
    }

    protected void doExecuteCallback(TaskEvent taskEvent){
        //获取回调地址
        WorkflowInstanceService instanceService = SpringUtil.getBean(WorkflowInstanceService.class);
        WorkflowInstance workflowInstance = instanceService.lambdaQuery()
                .eq(WorkflowInstance::getFlowableInsId, taskEvent.getProcessInsId())
                .one();
        if (ObjectUtils.isEmpty(workflowInstance.getListenerUrl())){
            return;
        }
        com.jeeplus.flsflow.task.service.TaskService taskService = SpringUtil.getBean(TaskService.class);
        ApiTaskReq req = new ApiTaskReq();
        req.setMethod(TaskMethod.POST);
        req.setRequestUrl(workflowInstance.getListenerUrl());
        req.setIdSource(taskEvent.getProcessInsId());
        req.setCodeSource(taskEvent.getProcessInsId());
        req.setSourceType(SourceType.PROCESS_INSTANCE+"_"+taskEvent.getEvent().toUpperCase());
        String idTask = taskService.addTask(req, JSON.toJSONString(taskEvent));

//        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
//            @Override
//            public void afterCommit() {
                TaskQuery taskQuery = new TaskQuery();
                taskQuery.setIdApiTask(idTask);
                taskService.target(taskQuery, null);
//            }
//        });
    }
}
