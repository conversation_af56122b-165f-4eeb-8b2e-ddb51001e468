package com.jeeplus.flsflow.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 错误代码枚举
 *
 * <AUTHOR>
 * @date 2023/4/26 12:48
 */
@Getter
@AllArgsConstructor
public enum ErrorCodeEnum {
    /**
     * 全局错误代码 小于0都是错误
     */
    success(200, "操作成功"),
    argument_error(-100, "参数错误"),
    request_method_error(-200, "请求类型错误"),
    token_error(-201, "token错误"),
    excel_error(-300, "excel异常"),
    biz_error(-1000, "操作失败"),
    system_error(-10000, "系统错误");

    private final Integer key;

    private final String value;
}
