package com.jeeplus.flsflow;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jeeplus.aop.logging.annotation.ApiLog;
import com.jeeplus.extension.service.dto.FlowButtonDTO;
import com.jeeplus.flowable.controller.FlowableFormController;
import com.jeeplus.flowable.controller.FlowableTaskController;
import com.jeeplus.flowable.model.Flow;
import com.jeeplus.flowable.model.TaskComment;
import com.jeeplus.flowable.service.FlowProcessService;
import com.jeeplus.flowable.service.FlowTaskService;
import com.jeeplus.flowable.vo.*;
import com.jeeplus.flsflow.enums.ApproveCodeEnum;
import com.jeeplus.flsflow.enums.ProcessCodeEnum;
import com.jeeplus.flsflow.service.BillProcessService;
import com.jeeplus.flsflow.service.FlsFlowService;
import com.jeeplus.flsflow.utils.ResultUtil;
import com.jeeplus.flsflow.vo.*;
import com.jeeplus.sys.utils.UserUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.flowable.engine.HistoryService;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.task.api.Task;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

/**
 * 单据流程controller
 *
 * <AUTHOR>
 */
@Api(tags = "流程应用")
@Slf4j
@RestController
@RequestMapping("/bill/process")
public class BillProcessController {

    @Autowired
    private BillProcessService billProcessService;

    @Resource
    private FlowTaskService flowTaskService;

    @Resource
    private FlsFlowService flsFlowService;

    @Resource
    private FlowableFormController flowableFormController;

    @Resource
    private FlowableTaskController flowableTaskController;

    @Resource
    private FlowProcessService flowProcessService;

    @Resource
    private HistoryService historyService;
    
    

    @ApiLog("发起流程")
    @ApiOperation("发起流程")
    @PostMapping("/start")
    public BaseResult<BillFlowResp> startProcess(@RequestBody @Valid BillFlowReq req) {
        return billProcessService.startProcess(req);
    }

    @ApiLog("流程审批")
    @ApiOperation("流程审批")
    @PostMapping("/approve")
    public BaseResult<BillFlowResp> approveProcess(@RequestBody @Valid BillFlowApproveReq req) {
        log.debug("流程审批流程入参:{}", JSONUtil.parseObj(req));

        //查询任务
        String userId = UserUtils.getCurrentUserDTO().getId();
//        Task task = flsFlowService.queryTaskByProcessInsId(req.getProcessInstanceId(), userId);
        Task task = flsFlowService.getTaskByProcessInsId(req.getProcessInstanceId());

        if (ObjectUtils.isEmpty(task)) {
            throw new RuntimeException("任务不存在");
        }
        Integer result = req.getResult();
        if (result == null) {
            throw new IllegalArgumentException("审批结果不能为空");
        }
        String remarks = req.getRemarks();
        Flow flow = new Flow();
        flow.setProcInsId(req.getProcessInstanceId());
        flow.setTaskId(task.getId());
        flow.setComment(new TaskComment());
        flow.getComment().setMessage(remarks);
        if (!ObjectUtils.isEmpty(req.getUserIds())) {
            flow.setAssignee(String.join(",", req.getUserIds()));
        }
        ApproveCodeEnum approveCodeEnum = ApproveCodeEnum.getEnumByCode(result);
        switch (approveCodeEnum) {
            case SUBMITTED:
                HistoricProcessInstance hi = historyService.createHistoricProcessInstanceQuery()
                        .processInstanceId(req.getProcessInstanceId())
                        .singleResult();
                String startUserId = hi.getStartUserId();
                if (userId.equalsIgnoreCase(startUserId) && "提交申请".equals(task.getName())) {
                    // 如果传了 动作类型，默认执行的首次提交
                    if (!ObjectUtils.isEmpty(req.getActionType()) && ActionTypeEnum.RECOMMIT.getName().equalsIgnoreCase(req.getActionType())) {
                        flow.getComment().setCommentType(TaskComment.prefix + ActionType.RECOMMIT.getType());
                        flow.getComment().setStatus(ActionType.RECOMMIT.getStatus());
                        flow.getComment().setMessage(StringUtils.isEmpty(req.getRemarks()) ? "重新提交申请" : req.getRemarks());
                    } else {
                        flow.getComment().setCommentType(TaskComment.prefix + ActionType.COMMIT.getType());
                        flow.getComment().setStatus(ActionType.COMMIT.getStatus());
                        flow.getComment().setMessage(StringUtils.isEmpty(req.getRemarks()) ? "提交申请" : req.getRemarks());
                    }
                } else {
                    flow.getComment().setCommentType(TaskComment.prefix + ActionType.AGREE.getType());
                    flow.getComment().setStatus(ActionType.AGREE.getStatus());
                    flow.getComment().setMessage(StringUtils.isEmpty(req.getRemarks()) ? "审批同意" : req.getRemarks());
                }
                flowableFormController.submitTaskFormData(flow, JSON.toJSONString(req.getData()));
                break;
            case REJECTED:
                // 驳回
                flow.getComment().setCommentType(TaskComment.prefix + ActionType.REJECT.getType());
                flow.getComment().setStatus(ActionType.REJECT.getStatus());
                if (!ObjectUtils.isEmpty(req.getActionType()) && ActionTypeEnum.REJECT_CREATOR.getName().equalsIgnoreCase(req.getActionType())) {
                    // 如果传了 动作类型，且是REJECT_CREATOR 代表要退回到制单人
                    flowTaskService.rejected(req.getProcessInstanceId(), task.getId(), flow.getComment(), ActionTypeEnum.REJECT_CREATOR.getName());
                }else{
                    flowTaskService.rejected(req.getProcessInstanceId(), task.getId(), flow.getComment(), ActionTypeEnum.REJECT.getName());
                }
                break;
            case DRAFT:
                // 草稿
                flow.getComment().setCommentType(TaskComment.prefix + ActionType.SAVE.getType());
                flow.getComment().setStatus(ActionType.SAVE.getStatus());
                flowableFormController.submitTaskFormData(flow, JSON.toJSONString(req.getData()));
                break;
            default:
                flowableFormController.submitTaskFormData(flow, JSON.toJSONString(req.getData()));
        }
        return ResultUtil.ok(billProcessService.getBillFlowResp(req.getProcessInstanceId()));
    }

    @ApiLog("我的待办")
    @ApiOperation("我的待办")
    @PostMapping("/todo")
    public BaseResult<Page<ProcessVo>> todo(@RequestBody BillFlowTodoReq req) {
        return billProcessService.todo(req);
    }

    @ApiLog("流程明细")
    @GetMapping("/detail")
    @ApiOperation("流程审批历史任务")
    public BaseResult<FlowVo> historicTaskList(@ApiParam(value = "审批实例id", required = true) String processInstanceId) throws Exception {
        if (ObjectUtils.isEmpty(processInstanceId)) {
            return ResultUtil.error("processInstanceId不能为空");
        }
        List<Flow> historicTaskList = flowTaskService.historicTaskList(processInstanceId);
        FlowVo flowVo = new FlowVo();
        flowVo.setHistoryFlowList(historicTaskList);
        List<FlowNodeVo> flowNodeVos = new ArrayList<>(0);
        for (Flow flow : historicTaskList) {
            FlowNodeVo flowNodeVo = new FlowNodeVo();
            flowNodeVo.setNodeName(flow.getHistIns().getActivityName());
            flowNodeVo.setCreateTime(flow.getHistIns().getTime());
            flowNodeVo.setAssigneeName(flow.getAssigneeName());
            flowNodeVos.add(flowNodeVo);

        }
        flowVo.setFlowNodeList(flowNodeVos);
        return ResultUtil.ok(flowVo);
    }

    @ApiLog("流程撤回提交")
    @ApiOperation("流程撤回提交")
    @GetMapping("/revoke")
    public BaseResult<BillFlowResp> revoke(@ApiParam(value = "审批实例id", required = true) @RequestParam(required = true) String processInstanceId) throws Exception {
        if (ObjectUtils.isEmpty(processInstanceId)) {
            return ResultUtil.error("processInstanceId不能为空");
        }
        flowTaskService.revoke(processInstanceId);
        return ResultUtil.ok(billProcessService.getBillFlowResp(processInstanceId, ProcessCodeEnum.REVOKE));
    }

    @ApiLog("流程作废")
    @ApiOperation("流程作废")
    @PostMapping("/voided")
    public BaseResult<BillFlowResp> voided(@RequestBody @Valid BillFlowApproveReq req) {
        String userId = UserUtils.getCurrentUserDTO().getId();
        Task task = flsFlowService.queryTaskByProcessInsId(req.getProcessInstanceId(), userId);
        if (ObjectUtils.isEmpty(task)) {
            throw new RuntimeException("任务不存在");
        }
        flowProcessService.stopProcessInstanceById(req.getProcessInstanceId(), ProcessStatus.DELETED, StringUtils.isEmpty(req.getRemarks()) ? "流程作废" : req.getRemarks());
        return ResultUtil.ok(billProcessService.getBillFlowResp(req.getProcessInstanceId(), ProcessCodeEnum.VOIDED));
    }
    
    @ApiLog("退回制单人")
    @ApiOperation("退回制单人")
    @PostMapping("/reject/applicant")
    public BaseResult<BillFlowResp> rejectApplicant(@RequestBody @Valid BillFlowApproveReq req) {
        String userId = UserUtils.getCurrentUserDTO().getId();
        Task task = flsFlowService.queryTaskByProcessInsId(req.getProcessInstanceId(), userId);
        if (ObjectUtils.isEmpty(task)) {
            throw new RuntimeException("当前用户,任务不存在");
        }
        TaskComment comment = new TaskComment();
        comment.setMessage(StringUtils.isEmpty(req.getRemarks()) ? "退回制单人" : req.getRemarks());
        comment.setCommentType(TaskComment.prefix + ActionType.BACK.getType());
        comment.setStatus(ActionType.BACK.getStatus());
        flowTaskService.rejected(req.getProcessInstanceId(), task.getId(), comment, ActionTypeEnum.REJECT_CREATOR.getName());
        return ResultUtil.ok(billProcessService.getBillFlowResp(req.getProcessInstanceId()));
    }

    @ApiLog("退回上一审批人")
    @ApiOperation("退回上一审批人")
    @PostMapping("/rejectback")
    public BaseResult<BillFlowResp> rejectback(@RequestBody @Valid BillFlowApproveReq req) {
        String userId = UserUtils.getCurrentUserDTO().getId();
        Task task = flsFlowService.queryTaskByProcessInsId(req.getProcessInstanceId(), userId);
        if (ObjectUtils.isEmpty(task)) {
            throw new RuntimeException("当前用户,任务不存在");
        }
        List<Flow> nodes = flowTaskService.getBackNodes(task.getId());
        if (ObjectUtils.isEmpty(nodes)) {
            throw new RuntimeException("没有可以驳回的节点");
        }
        Flow backFlow = nodes.get(nodes.size() - 1);
        TaskComment comment = new TaskComment();
        comment.setMessage(StringUtils.isEmpty(req.getRemarks()) ? "退回上一审批人" : req.getRemarks());
        comment.setCommentType(TaskComment.prefix + ActionType.REJECT.getType());
        comment.setStatus(ActionType.REJECT.getStatus());
        flowTaskService.backTask(backFlow.getTaskDefKey(), task.getId(), comment, req.getUserIds());
        return ResultUtil.ok(billProcessService.getBillFlowResp(req.getProcessInstanceId()));
    }
    
    @ApiLog("审批不同意")
    @ApiOperation("审批不同意")
    @PostMapping("/approve/disagree")
    public BaseResult<BillFlowResp> approveDisagree(@RequestBody @Valid BillFlowApproveReq req) {
    	String userId = UserUtils.getCurrentUserDTO().getId();
        Task task = flsFlowService.queryTaskByProcessInsId(req.getProcessInstanceId(), userId);
        if (ObjectUtils.isEmpty(task)) {
            throw new RuntimeException("当前用户,任务不存在");
        }
        flowProcessService.stopProcessInstanceById(req.getProcessInstanceId(), ProcessStatus.STOP, StringUtils.isEmpty(req.getRemarks()) ? "审批不同意" : req.getRemarks());
        return ResultUtil.ok(billProcessService.getBillFlowResp(req.getProcessInstanceId(), ProcessCodeEnum.STOP));
    }

    @ApiLog("我的已办列表")
    @ApiOperation("我的已办列表")
    @PostMapping("/list")
    public BaseResult<Page<HisTaskVo>> historicListData(@RequestBody BillFlowTodoReq req) {
        return ResultUtil.ok(billProcessService.list(req));
    }

    @ApiLog("获取流程实例状态")
    @ApiOperation("获取流程实例状态")
    @GetMapping("/status")
    public BaseResult<Integer> getProcessStatus(@ApiParam(value = "审批实例id", required = true) @RequestParam(required = true) String processInstanceId) {
        return ResultUtil.ok(billProcessService.getProcessStatus(processInstanceId));
    }

    @ApiLog("流程转办")
    @ApiOperation("转办")
    @PostMapping("/transfer")
    public BaseResult<BillFlowResp> transferTask(@RequestBody @Valid TransferReq transferReq) {
        //查询任务
        String userId = UserUtils.getCurrentUserDTO().getId();
        Task task = flsFlowService.queryTaskByProcessInsId(transferReq.getProcessInstanceId(), userId);
        if (ObjectUtils.isEmpty(task)) {
            throw new RuntimeException("当前用户,任务不存在");
        }
        billProcessService.transferTask(task.getId(), transferReq);
        return ResultUtil.ok(billProcessService.getBillFlowResp(transferReq.getProcessInstanceId()));
    }

    @ApiLog("获取流程审批人")
    @ApiOperation("获取流程审批人")
    @GetMapping("/assignees")
    public BaseResult<?> getAssignees(@RequestParam(required = true) String processInstanceId) {
        return ResultUtil.ok(billProcessService.getAssignees(processInstanceId));
    }


    @ApiLog("下一步流程审批任务查询")
    @GetMapping("/getNextStepInfo")
    @ApiOperation("下一步流程审批任务查询")
    public BaseResult<?> getNextStepInfo(@ApiParam(value = "审批实例id", required = true) String processInstanceId) throws Exception {
        Task task = flsFlowService.getTaskByProcessInsId(processInstanceId);
        JSONObject result = new JSONObject();
        if (!ObjectUtils.isEmpty(task)) {
            result.put("id", task.getId());
            result.put("name", task.getName());
            result.put("processInstanceId", processInstanceId);
        }
        return ResultUtil.ok(result);
    }
    
    @ApiLog("流程按钮列表")
    @GetMapping("/button")
    @ApiOperation("流程审批历史任务")
    public BaseResult<List<FlowButtonDTO>> getTaskButton(@ApiParam(value = "审批实例id", required = true) String processInstanceId){
    	return ResultUtil.ok(this.billProcessService.getButtons(processInstanceId));
    }
}
