package com.jeeplus.flsflow.listener;

import cn.hutool.json.JSONUtil;
import com.jeeplus.flsflow.constant.MessageMqConstant;
import com.jeeplus.flsflow.domain.OrderTaskMessage;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.ExecutionListener;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 流程监听器，用于在流程执行到特定事件时发送消息到消息队列
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
@Slf4j
@Component("workOrderExecutionListener")
public class WorkOrderExecutionListener implements ExecutionListener {

    @Resource
    private RabbitTemplate rabbitTemplate;

    @Override
    public void notify(DelegateExecution execution) {
        //提取message属性
        String processInstanceId = execution.getProcessInstanceId();
        OrderTaskMessage message = OrderTaskMessage.builder().eventName(execution.getEventName()).idProcInst(processInstanceId).build();
        try {
            //发送消息到消息队列
            rabbitTemplate.convertAndSend(MessageMqConstant.WORKORDER_TASK_EXCHANGE, MessageMqConstant.WORKORDER_TASK_TOPIC, JSONUtil.toJsonStr(message),
                    new CorrelationData(processInstanceId));
            //日志打点
            log.info("success send complete message to mq: {}", JSONUtil.toJsonStr(message));
        }
        catch (Exception e) {
            log.error("failed to send todo message, error:{},message:{}", e.getMessage(), JSONUtil.toJsonStr(message));
        }
    }
}
