package com.jeeplus.flsflow.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.flowable.ui.modeler.model.ModelRepresentation;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * created 2023/11/10 15:21:41
 */
@Data
public class WorkflowBindReq {
    @ApiModelProperty(value = "工作流程id", required = true)
    @NotBlank(message = "workflowModelId不能为空")
    private String workflowModelId;

    @ApiModelProperty("流程模型id")
    @NotBlank(message = "flowableModelId不能为空")
    private String flowableModelId;
}
