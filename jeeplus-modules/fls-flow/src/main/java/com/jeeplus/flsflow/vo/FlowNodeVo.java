package com.jeeplus.flsflow.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * created 2023/12/21 11:38:28
 */
@Data
@ApiModel
public class FlowNodeVo {
    @ApiModelProperty("节点名称")
    private String nodeName;

    @ApiModelProperty("节点执行人姓名")
    private String assigneeName;

    @ApiModelProperty("任务节点创建时间")
    private Date createTime;
}
