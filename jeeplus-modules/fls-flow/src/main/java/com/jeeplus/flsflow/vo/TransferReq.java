package com.jeeplus.flsflow.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * created 2023/11/7 22:38:18
 */
@Data
public class TransferReq {
    @ApiModelProperty("流程实例id")
    @NotBlank
    private String processInstanceId;

    @ApiModelProperty("审批人用户id")
    private String userId;
    
    private List<String> userIds;

}
