package com.jeeplus.flsflow.listener;

import cn.hutool.extra.spring.SpringUtil;
import com.jeeplus.flsflow.domain.TaskEvent;
import com.jeeplus.flsflow.domain.WorkflowInstance;
import com.jeeplus.flsflow.service.WorkflowInstanceService;
import com.jeeplus.security.util.UserSessionDto;
import com.jeeplus.security.util.UserSessionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * created 2023/12/3 17:37:29
 */
@Component
@Async
@Slf4j
public class FlsTaskProxyListener extends AbstractListener {

    @Async(value = "manualNotifyTask")
    public void manualNotify(TaskEvent taskEvent, UserSessionDto userSessionDto, boolean retry) {
        try {
            UserSessionUtils.put(userSessionDto);
            TimeUnit.SECONDS.sleep(2);
            WorkflowInstanceService instanceService = SpringUtil.getBean(WorkflowInstanceService.class);
            WorkflowInstance workflowInstance = instanceService.lambdaQuery()
                    .eq(WorkflowInstance::getFlowableInsId, taskEvent.getProcessInsId())
                    .one();
            if (ObjectUtils.isEmpty(workflowInstance) && retry){
                manualNotify(taskEvent, userSessionDto, false);
                return;
            }
            doTaskCallback(taskEvent, workflowInstance);
        }catch (Exception e){
            log.error("手动执行回调失败",e);
        }
    }
}
