package com.jeeplus.flsflow.task.enums;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import org.springframework.util.ObjectUtils;

import java.io.IOException;

/**
 * <AUTHOR>
 * created 2023/11/25 11:45:46
 */
public class SourceTypeConverter extends JsonDeserializer<SourceType> {

    @Override
    public SourceType deserialize(JsonParser jsonParser, DeserializationContext ctxt) throws IOException, JsonProcessingException {
        JsonNode jsonNode = jsonParser.getCodec().readTree(jsonParser);
        if (ObjectUtils.isEmpty(jsonNode.asText())){
            return null;
        }
        return SourceType.valueOf(jsonNode.asText());
    }
}
