package com.jeeplus.flsflow.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * created 2023/11/11 21:06:59
 */
@Data
public class EnableReq {
    @ApiModelProperty("流程key")
    @NotBlank(message = "流程key不能为空")
    private String key;

    @ApiModelProperty("active-激活,suspend-挂起")
    @NotBlank(message = "操作状态不能为空")
    private String status;
}
