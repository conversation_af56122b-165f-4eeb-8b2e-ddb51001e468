package com.jeeplus.flsflow.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.*;
import java.util.Set;

/**
 * <AUTHOR>
 * created 2023/11/10 15:21:41
 */
@Data
public class WorkflowUpdateReq {
    @ApiModelProperty("流程名称")
    @NotBlank(message = "名称不能为空")
    private String name;

    @ApiModelProperty(value = "条件类型:1-经营主体, 默认:1", required = true)
    private Integer conditionType = 1;

    @ApiModelProperty("条件业务id")
    @NotEmpty(message = "条件业务id值不能为空")
    private Set<String> bizIdList;

    @ApiModelProperty(value = "工作流程id", required = true)
    @NotBlank(message = "id不能为空")
    private String id;

    @ApiModelProperty(value = "是否启用,1-启用,2-禁用", required = true)
    @NotNull(message = "status不能为空")
    @Min(value = 1, message = "状态范围:1-启用,2-禁用")
    @Max(value = 2, message = "状态范围:1-启用,2-禁用")
    private Integer status;
}
