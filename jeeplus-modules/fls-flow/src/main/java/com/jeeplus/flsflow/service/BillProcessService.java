package com.jeeplus.flsflow.service;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.jeeplus.extension.domain.FlowButton;
import com.jeeplus.extension.domain.TaskDefExtension;
import com.jeeplus.extension.service.FlowButtonService;
import com.jeeplus.extension.service.TaskDefExtensionService;
import com.jeeplus.extension.service.dto.FlowButtonDTO;
import com.jeeplus.extension.service.mapstruct.FlowButtonWrapper;
import com.jeeplus.flowable.constant.FlowableConstant;
import com.jeeplus.flowable.controller.FlowableFormController;
import com.jeeplus.flowable.model.BillWorkflow;
import com.jeeplus.flowable.model.TaskComment;
import com.jeeplus.flowable.service.BillWorkflowService;
import com.jeeplus.flowable.service.FlowProcessService;
import com.jeeplus.flowable.service.FlowTaskService;
import com.jeeplus.flowable.utils.ProcessDefCache;
import com.jeeplus.flowable.vo.ActionType;
import com.jeeplus.flowable.vo.HisTaskVo;
import com.jeeplus.flowable.vo.ProcessVo;
import com.jeeplus.flowable.vo.TaskVo;
import com.jeeplus.flsflow.enums.ProcessCodeEnum;
import com.jeeplus.flsflow.utils.ResultUtil;
import com.jeeplus.flsflow.vo.BaseResult;
import com.jeeplus.flsflow.vo.BillFlowReq;
import com.jeeplus.flsflow.vo.BillFlowResp;
import com.jeeplus.flsflow.vo.BillFlowTodoReq;
import com.jeeplus.flsflow.vo.TransferReq;
import com.jeeplus.sys.constant.CommonConstants;
import com.jeeplus.sys.utils.UserUtils;

import org.apache.commons.lang3.StringUtils;
import org.flowable.bpmn.model.Process;
import org.flowable.common.engine.impl.identity.Authentication;
import org.flowable.engine.HistoryService;
import org.flowable.engine.RepositoryService;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.TaskService;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.repository.ProcessDefinition;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.identitylink.api.IdentityLink;
import org.flowable.identitylink.api.IdentityLinkType;
import org.flowable.task.api.Task;
import org.flowable.task.api.TaskQuery;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.flowable.task.api.history.HistoricTaskInstanceQuery;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class BillProcessService {
    @Autowired
    private FlowableFormController flowableFormController;

    @Autowired
    private BillWorkflowService billWorkflowService;

    @Autowired
    private FlowProcessService flowProcessService;

    @Autowired
    private FlsFlowService flsFlowService;

    @Autowired
    private TaskService taskService;

    @Autowired
    private HistoryService historyService;

    @Autowired
    private FlowTaskService flowTaskService;

    @Autowired
    private RuntimeService runtimeService;
    
    @Autowired
    private FlowButtonService flowButtonService;
    
    @Autowired
    private TaskDefExtensionService taskDefExtensionService;

    public BaseResult<BillFlowResp> startProcess(BillFlowReq req) {
        // 1、根据单据编码和交易类型查询单据
        // 2、如果1不存在 则根据单据编码查询单据
        BillWorkflow billWorkflow = billWorkflowService.lambdaQuery().eq(BillWorkflow::getBillCode, req.getBillCode())
                .eq(!ObjectUtils.isEmpty(req.getTransTypeCode()), BillWorkflow::getTransTypeCode, req.getTransTypeCode())
                .eq(BillWorkflow::getDelFlag, CommonConstants.NO).eq(BillWorkflow::getStatus, "2")
                .one();
        if (req.isFindHead() && billWorkflow == null) {
            billWorkflow = billWorkflowService.lambdaQuery()
                    .eq(BillWorkflow::getBillCode, req.getBillCode())
                    .eq(BillWorkflow::getTransTypeCode, FlowableConstant.NULL_STR)
                    .eq(BillWorkflow::getDelFlag, CommonConstants.NO)
                    .eq(BillWorkflow::getStatus, "2").one();
        }
        // 3、如果都不存在 则提示单据流程还未注册
        if (ObjectUtils.isEmpty(billWorkflow)) {
            return ResultUtil.error("单据流程还未注册");
        }
        ProcessDefinition processDefinition = flowProcessService
                .getProcessDefinitionByKey(billWorkflow.getFlowableModelKey());
        ResponseEntity<String> entity = flowableFormController.submitStartFormData(null, processDefinition.getId(),
                req.getTitle(), JSON.toJSONString(req.getData()));
        String processInstanceId = entity.getBody();
        // 查询所有节点
//		List <FlowNode> nodes = flowableBpmnModelService.findFlowNodes( processDefinition.getId());
//		for(FlowNode node : nodes) {
//			// 如果节点是提交申请 直接执行审批
//			if(CommonConstants.SUBMIT.equals(node.getName())) {
//				String userId = UserUtils.getCurrentUserDTO().getId();
//		        Task task = flsFlowService.queryTaskByProcessInsId(processInstanceId, userId);
//				Flow flow = new Flow();
//		        flow.setProcInsId(processInstanceId);
//		        flow.setTaskId(task.getId());
//		        flow.setComment(new TaskComment());
//		        flow.getComment().setMessage("提交申请");
//		        flow.getComment().setCommentType(TaskComment.prefix + ActionType.COMMIT.getType());
//	        	flow.getComment().setStatus(ActionType.COMMIT.getStatus());
//				flowableFormController.submitTaskFormData(flow, JSON.toJSONString(req.getData()));
//				break;
//			}
//		}
        return ResultUtil.ok(this.getBillFlowResp(processInstanceId));
    }

    public BaseResult<Page<ProcessVo>> todo(BillFlowTodoReq req) {
        String idUser = UserUtils.getCurrentUserDTO().getId();
        Page<ProcessVo> page = new Page<ProcessVo>();
        page.setSize(req.getSize());
        page.setCurrent(req.getCurrent());
        // =============== 已经签收或者等待签收的任务 ===============
        TaskQuery todoTaskQuery = taskService.createTaskQuery().taskCandidateOrAssigned(idUser).active()
                .includeProcessVariables().taskDefinitionKeyLike("UserTask%").orderByTaskCreateTime().desc();
        List<String> processDefinitionKeys = null;
        if (!ObjectUtils.isEmpty(req.getBillCode())) {
            List<BillWorkflow> billWorkflowList = billWorkflowService.lambdaQuery()
                    .eq(BillWorkflow::getBillCode, req.getBillCode())
                    .eq(!ObjectUtils.isEmpty(req.getTransTypeCode()), BillWorkflow::getTransTypeCode, req.getTransTypeCode())
                    .eq(BillWorkflow::getDelFlag, CommonConstants.NO)
                    .list();
            if (!ObjectUtils.isEmpty(billWorkflowList)) {
                processDefinitionKeys = billWorkflowList.stream().map(BillWorkflow::getFlowableModelKey)
                        .collect(Collectors.toList());
            } else {
                return ResultUtil.ok(page);
            }
        }
        // 设置查询条件
        if (!ObjectUtils.isEmpty(processDefinitionKeys)) {
            todoTaskQuery.processDefinitionKeyIn(processDefinitionKeys);
        }

        long total = todoTaskQuery.count();
        page.setTotal(total);
        List<Task> todoList;
        // 查询列表
        if (page.getSize() == -1) {// 不分页
            todoList = todoTaskQuery.list();
        } else {
            int start = (int) ((page.getCurrent() - 1) * page.getSize());
            int size = (int) (page.getSize());
            todoList = todoTaskQuery.listPage(start, size);
        }
        List<ProcessVo> records = Lists.newArrayList();
        for (Task task : todoList) {
            Process process = SpringUtil.getBean(RepositoryService.class).getBpmnModel(task.getProcessDefinitionId())
                    .getMainProcess();
            ProcessVo processVo = new ProcessVo();
            TaskVo taskVo = new TaskVo(task);
            taskVo.setProcessDefKey(process.getId());
            processVo.setTask(taskVo);
            processVo.setVars(task.getProcessVariables());
            processVo.setProcessDefinitionName(ProcessDefCache.get(task.getProcessDefinitionId()).getName());
            processVo.setVersion(ProcessDefCache.get(task.getProcessDefinitionId()).getVersion());
            processVo.setStatus("todo");
            records.add(processVo);
        }
        page.setRecords(records);
        return ResultUtil.ok(page);
    }

    public Page<HisTaskVo> list(BillFlowTodoReq req) {
        Page<HisTaskVo> page = new Page<HisTaskVo>();
        page.setSize(req.getSize());
        page.setCurrent(req.getCurrent());
        String userId = UserUtils.getCurrentUserDTO().getId();
        HistoricTaskInstanceQuery histTaskQuery = historyService.createHistoricTaskInstanceQuery().taskAssignee(userId)
                .finished().includeProcessVariables().orderByHistoricTaskInstanceEndTime().desc();
        // 设置查询条件
        List<String> processDefinitionKeys = null;
        if (!ObjectUtils.isEmpty(req.getBillCode())) {
            List<BillWorkflow> billWorkflowList = billWorkflowService.lambdaQuery()
                    .eq(BillWorkflow::getBillCode, req.getBillCode())
                    .eq(!ObjectUtils.isEmpty(req.getTransTypeCode()), BillWorkflow::getTransTypeCode, req.getTransTypeCode())
                    .eq(BillWorkflow::getDelFlag, CommonConstants.NO)
                    .list();
            if (!ObjectUtils.isEmpty(billWorkflowList)) {
                processDefinitionKeys = billWorkflowList.stream().map(BillWorkflow::getFlowableModelKey)
                        .collect(Collectors.toList());
            } else {
                return page;
            }
        }
        if (!ObjectUtils.isEmpty(processDefinitionKeys)) {
            histTaskQuery.processDefinitionKeyIn(processDefinitionKeys);
        }
        if (req.getBeginDate() != null) {
            histTaskQuery.taskCompletedAfter(req.getBeginDate());
        }
        if (req.getEndDate() != null) {
            histTaskQuery.taskCompletedBefore(req.getEndDate());
        }
        if (req.getTitle() != null) {
            histTaskQuery.processVariableValueLike(FlowableConstant.TITLE, "%" + req.getTitle() + "%");
        }
        // 查询总数
        page.setTotal(histTaskQuery.count());
        // 查询列表
        List<HistoricTaskInstance> histList;
        if (page.getSize() == -1) {
            histList = histTaskQuery.list();
        } else {
            int start = (int) ((page.getCurrent() - 1) * page.getSize());
            int size = (int) (page.getSize());
            histList = histTaskQuery.listPage(start, size);
        }
        List records = Lists.newArrayList();
        for (HistoricTaskInstance histTask : histList) {
            HisTaskVo hisTaskVo = new HisTaskVo(histTask);
            hisTaskVo.setProcessDefinitionName(ProcessDefCache.get(histTask.getProcessDefinitionId()).getName());
            hisTaskVo.setBack(flowTaskService.isBack(histTask));
            List<Task> currentTaskList = taskService.createTaskQuery()
                    .processInstanceId(histTask.getProcessInstanceId()).list();
            if (((List) currentTaskList).size() > 0) {
                TaskVo currentTaskVo = new TaskVo(currentTaskList.get(0));
                hisTaskVo.setCurrentTask(currentTaskVo);
            }
            // 获取意见评论内容
            List<TaskComment> commentList = flowTaskService.getTaskComments(histTask.getId());
            if (commentList.size() > 0) {
                TaskComment comment = commentList.get(commentList.size() - 1);
                hisTaskVo.setComment(comment.getMessage());
                hisTaskVo.setLevel(comment.getLevel());
                hisTaskVo.setType(comment.getType());
                hisTaskVo.setStatus(comment.getStatus());
            }
            records.add(hisTaskVo);
        }
        page.setRecords(records);
        return page;
    }

    public Integer getProcessStatus(String processInstanceId) {
        HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery()
                .processInstanceId(processInstanceId)
                .singleResult();
        if (historicProcessInstance == null) {
            throw new IllegalArgumentException("流程实例不存在");
        }
        // 也可以判断结束时间是否为空
        if (historicProcessInstance.getEndTime() != null) {
            return ProcessCodeEnum.COMPLETED.getCode();
        }
        return ProcessCodeEnum.WAITING.getCode();
    }

    public BillFlowResp getBillFlowResp(String processInstanceId) {
        BillFlowResp billFlowResp = new BillFlowResp();
        billFlowResp.setProcessInstanceId(processInstanceId);
        billFlowResp.setStatus(this.getProcessStatus(processInstanceId));
        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                .processInstanceId(processInstanceId)
                .singleResult();
        if (processInstance == null) {
            return billFlowResp;
        }
        List<Task> tasks = taskService.createTaskQuery()
                .processInstanceId(processInstance.getId())
                .list();
        if (ObjectUtils.isEmpty(tasks)) {
            throw new IllegalArgumentException("审批任务不存在");
        }
        List<String> assignees = new ArrayList<String>();
        for (Task task : tasks) {
            // 执行后续操作或展示审批人信息
            taskService.getIdentityLinksForTask(task.getId()).forEach(identityLink -> {
                assignees.add(identityLink.getUserId());
            });
        }
        billFlowResp.setUserIds(assignees);
        TaskVo task = new TaskVo();
        task.setName(tasks.get(0).getName());
        billFlowResp.setTask(task);
        return billFlowResp;
    }
    
    public BillFlowResp getBillFlowResp(String processInstanceId, ProcessCodeEnum codeEnum) {
        BillFlowResp billFlowResp = new BillFlowResp();
        billFlowResp.setProcessInstanceId(processInstanceId);
        billFlowResp.setStatus(codeEnum.getCode());
        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                .processInstanceId(processInstanceId)
                .singleResult();
        if (processInstance == null) {
            return billFlowResp;
        }
        List<Task> tasks = taskService.createTaskQuery()
                .processInstanceId(processInstance.getId())
                .list();
        if (ObjectUtils.isEmpty(tasks)) {
            throw new IllegalArgumentException("审批任务不存在");
        }
        List<String> assignees = new ArrayList<String>();
        for (Task task : tasks) {
            // 执行后续操作或展示审批人信息
            taskService.getIdentityLinksForTask(task.getId()).forEach(identityLink -> {
                assignees.add(identityLink.getUserId());
            });
        }
        billFlowResp.setUserIds(assignees);
        TaskVo task = new TaskVo();
        task.setName(tasks.get(0).getName());
        billFlowResp.setTask(task);
        return billFlowResp;
    }

    public List<TaskVo> getTasks(String processInstanceId) {
        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                .processInstanceId(processInstanceId).singleResult();
        List<Task> tasks = taskService.createTaskQuery().processInstanceId(processInstance.getId()).list();
        List<TaskVo> entityList = new ArrayList<TaskVo>();
        for (Task task : tasks) {
            TaskVo taskEntity = new TaskVo();
            taskEntity.setName(task.getName());
            entityList.add(taskEntity);
        }
        return entityList;
    }

    public List<String> getAssignees(String processInstanceId) {
        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                .processInstanceId(processInstanceId)
                .singleResult();
        List<Task> tasks = taskService.createTaskQuery()
                .processInstanceId(processInstance.getId())
                .list();
        List<String> assignees = new ArrayList<String>();
        for (Task task : tasks) {
            String assignee = task.getAssignee();
            // 执行后续操作或展示审批人信息
            assignees.add(assignee);
        }
        return assignees;
    }
    
	public List<FlowButtonDTO> getButtons(String processInstanceId) {
		ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
				.processInstanceId(processInstanceId).singleResult();
		String userId = UserUtils.getCurrentUserDTO().getId();;
		Task task = flsFlowService.queryTaskByProcessInsId(processInstanceId, userId);
		// 如果没有审批节点  返回空按钮列表
		if(task == null) {
			return new ArrayList<FlowButtonDTO>();
		}
		List<TaskDefExtension> list = taskDefExtensionService.lambdaQuery()
				.eq(TaskDefExtension::getProcessDefId, processInstance.getProcessDefinitionKey())
				.eq(TaskDefExtension::getTaskDefId, task.getTaskDefinitionKey()).list();
		if (list.size() > 1) {
			throw new IllegalArgumentException("重复的task id定义!");
		}
		if (list.size() < 0) {
			throw new IllegalArgumentException("当前流程节点未定义!");
		}
		return flowButtonService.lambdaQuery().eq(FlowButton::getTaskDefId, list.get(0).getId()).list().stream()
				.map(FlowButtonWrapper.INSTANCE::toDTO).collect(Collectors.toList());
	}
	
	public void transferTask(String taskId, TransferReq req) {
		if (CollectionUtils.isEmpty(req.getUserIds()) || StringUtils.isBlank(taskId)) {
			throw new IllegalArgumentException("参数错误");
		}
        // 设置当前流程任务办理人
        Authentication.setAuthenticatedUserId ( UserUtils.getCurrentUserDTO ( ).getId ( ) );
        taskService.addComment(taskId, req.getProcessInstanceId(), ActionType.TRANSFER.getType(), "流程转签");
        List<IdentityLink> currentLinks = taskService.getIdentityLinksForTask(taskId);
        for (IdentityLink currentLink : currentLinks) {
            if (currentLink.getUserId() != null) {
                taskService.deleteUserIdentityLink(taskId, currentLink.getUserId(), IdentityLinkType.CANDIDATE);
                taskService.deleteUserIdentityLink(taskId, currentLink.getUserId(), IdentityLinkType.ASSIGNEE);
                taskService.deleteUserIdentityLink(taskId, currentLink.getUserId(), IdentityLinkType.PARTICIPANT);
                taskService.deleteUserIdentityLink(taskId, currentLink.getUserId(), IdentityLinkType.OWNER);
            }
        }
        for(String userId : req.getUserIds()) {
        	taskService.addUserIdentityLink(taskId, userId, IdentityLinkType.CANDIDATE);
        }
    }
}
