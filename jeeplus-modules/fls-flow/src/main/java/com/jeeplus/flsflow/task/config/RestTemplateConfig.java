package com.jeeplus.flsflow.task.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.web.client.RestTemplate;

import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @date 2023/2/2
 * @since 1.0.0
 */
@Configuration
public class RestTemplateConfig {
    @ConditionalOnMissingBean(RestTemplate.class)
    @Bean
    public RestTemplate restTemplate(){
        RestTemplate restTemplate = new RestTemplate();
        for (HttpMessageConverter<?> messageConverter : restTemplate.getMessageConverters()) {
            if (messageConverter instanceof StringHttpMessageConverter){
                //解决中文乱码问题
                StringHttpMessageConverter converter = new StringHttpMessageConverter(StandardCharsets.UTF_8);
                int idx = restTemplate.getMessageConverters().indexOf(messageConverter);
                restTemplate.getMessageConverters().set(idx,converter);
                break;
            }
        }
        return restTemplate;
    }
}
