package com.jeeplus.flsflow.vo.form;

import com.alibaba.fastjson2.JSONObject;

import lombok.Data;

@Data
public class Options {
	private String width = "";
	private String defaultValue = "";
	private boolean required = false;
	private String requiredMessage = "";
	private String dataType = "";
	private boolean dataTypeCheck = false;
	private String dataTypeMessage = "";
	private String pattern = "";
	private boolean patternCheck = false;
	private String patternMessage = "";
	private boolean validatorCheck = false;
	private String validator = "";
	private String placeholder = "";
	private String customClass = "";
	private boolean disabled = false;
	private int labelWidth = 100;
	private boolean isLabelWidth = false;
	private boolean hidden = false;
	private boolean dataBind = true;
	private boolean showPassword = false;
	private boolean clearable = false;
	private String maxlength = "";
	private boolean showWordLimit = false;
	private JSONObject customProps = new JSONObject();
	private boolean isShow = true;
	private boolean isSort = true;
	private boolean isSearch = true;
	private String remoteFunc = "func_";
	private String remoteOption = "option_";
	private boolean tableColumn = false;
	private boolean subform = false;
	
	private Options() {
		
	}
	
	public static Options build(String key) {
		Options options = new Options();
		options.setRemoteFunc(options.getRemoteFunc() + key);
		options.setRemoteOption(options.getRemoteOption() + key);
		return options;
	}
}
