package com.jeeplus.flsflow.task.web;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jeeplus.flsflow.task.domain.ApiTaskReq;
import com.jeeplus.flsflow.task.domain.TaskPageQuery;
import com.jeeplus.flsflow.task.domain.TaskQuery;
import com.jeeplus.flsflow.task.domain.entity.ApiTask;
import com.jeeplus.flsflow.task.service.TaskService;
import com.jeeplus.flsflow.utils.ResultUtil;
import com.jeeplus.flsflow.vo.BaseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.concurrent.Future;

/**
 * <AUTHOR>
 * created 2023/11/25 10:37:09
 */
@Api(tags = "API任务-管理")
@RestController
@RequestMapping(value ="/fls")
public class TaskManagerController {

    @Resource
    private TaskService taskService;

    @PostMapping("/apiTask/add")
    @ApiOperation("添加任务")
    public BaseResult<ApiTask> add(@RequestBody @Valid ApiTaskReq req) {
        ApiTask apiTask = taskService.addTask(req, new TestArgBuilder());
        return ResultUtil.ok(apiTask);
    }

    @PostMapping("/apiTask/query")
    @ApiOperation("分页查询任务")
    public BaseResult<Page<ApiTask>> query(@RequestBody @Valid TaskPageQuery pageQuery) {
        Page<ApiTask> apiTaskList = taskService.query(pageQuery);
        return ResultUtil.ok(apiTaskList);
    }

    @PostMapping("/apiTask/queryOne")
    @ApiOperation("指定查询一个任务")
    public BaseResult<ApiTask> queryOne(@RequestBody @Valid TaskQuery query) {
        ApiTask apiTask = taskService.queryOneTask(query);
        return ResultUtil.ok(apiTask);
    }

    @PostMapping("/apiTask/target")
    @ApiOperation("触发任务")
    public BaseResult<ApiTask> target(@RequestBody @Valid TaskQuery taskQuery) {
        ApiTask apiTask = taskService.target(taskQuery, null);
        return ResultUtil.ok(apiTask);
    }

    @PostMapping("/apiTask/asyncTarget")
    @ApiOperation("异步触发任务")
    public BaseResult<ApiTask> asyncTarget(@RequestBody @Valid TaskQuery taskQuery) {
        Future<ApiTask> apiTask = taskService.asyncTarget(taskQuery, null);
        try {
            return ResultUtil.ok("suc");
        } catch (Exception e) {
            throw new RuntimeException("触发任务获取结果异常");
        }
    }
}
