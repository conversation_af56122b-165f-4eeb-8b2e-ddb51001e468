package com.jeeplus.flsflow.enums;

import com.jeeplus.flowable.vo.ActionType;

/**
 * <AUTHOR>
 * created 2023/11/8 17:42:28
 */
public enum ReviewType {
    AGREE,
    REJECT;

    public ActionType toActionType(){
        switch (this) {
            case AGREE:
                return ActionType.AGREE;
            case REJECT:
                return ActionType.REJECT;
            default:
                return null;
        }
    }

}
