package com.jeeplus.flsflow.vo;

import java.util.List;

import javax.validation.constraints.NotBlank;

import cn.hutool.json.JSONObject;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class BillFlowApproveReq {
	@ApiModelProperty("流程实例id")
    @NotBlank
    private String processInstanceId;

    @ApiModelProperty("流程表单入参,JSON格式")
    private JSONObject data;
    
    @ApiModelProperty("审批说明")
    private String remarks;
    
    @ApiModelProperty("审批结果")
    private Integer result;

    @ApiModelProperty("动作类型")
    private String actionType;
    
    private List<String> userIds;
}
