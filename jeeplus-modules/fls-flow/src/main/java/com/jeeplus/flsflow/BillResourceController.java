package com.jeeplus.flsflow;

import java.util.List;

import javax.validation.Valid;

import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jeeplus.aop.logging.annotation.ApiLog;
import com.jeeplus.extension.domain.BillMetadata;
import com.jeeplus.flsflow.domain.BillTreeNode;
import com.jeeplus.flsflow.service.BillResourceService;
import com.jeeplus.flsflow.service.ResourceTreeService;
import com.jeeplus.flsflow.utils.ResultUtil;
import com.jeeplus.flsflow.vo.BaseResult;
import com.jeeplus.flsflow.vo.BillResourceVo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 单据资源controller
 *
 * <AUTHOR>
 *
 */
@Api(tags = "流程应用")
@RestController
@RequestMapping("/bill")
public class BillResourceController {
	@Autowired
	private BillResourceService billResourceService;
	
	@Autowired
	private ResourceTreeService resourceTreeService;

	@ApiLog("单据资源注册")
	@ApiOperation("单据资源注册")
	@PostMapping("/resource")
	public BaseResult<String> init(@RequestBody @Valid BillResourceVo billResource) {
		billResourceService.init(billResource);
		return ResultUtil.ok("注册成功");
	}

	@ApiOperation("获取单据资源元数据")
	@GetMapping("/metadata")
	public BaseResult<BillMetadata> getBillResource(
			@RequestParam(required = true, name = "billCode") String billCode) {
		if(ObjectUtils.isEmpty(billCode)) {
			throw new IllegalArgumentException("单据编码不能为空");
		}
		return ResultUtil.ok(billResourceService.getBillMetadata(billCode));
	}
	
	@ApiOperation("获取单据交易类型树形结构")
	@GetMapping("/tree")
	public BaseResult<List<BillTreeNode>> getBillTransTree() {
		List<String> idResources = billResourceService.getResourceIds();
		return ResultUtil.ok(resourceTreeService.getTree(idResources));
	}
	
	@GetMapping("/resource/list")
	public ResponseEntity processListData(Page<BillMetadata> page, BillMetadata billMetadata) {
		return ResponseEntity.ok(billResourceService.page(page, billMetadata));
	}
}
