package com.jeeplus.flsflow.vo.form;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;

import lombok.Data;

@Data
public class Node {
	private String type = "input";
	private String icon = "icon-input";
	private String dsId = "";
	private String tableName = "";
	private String primaryKey = "";
	private String foreignKey = "";
	private Options options;
	private JSONObject events = initEvents();
	private String name = "";
	private String key = "";
	private String model = "";
	private JSONArray rules = new JSONArray();
	
	private Node() {
		
	}

	private JSONObject initEvents() {
		JSONObject json = new JSONObject();
		json.put("onChange", "");
		json.put("onFocus", "");
		json.put("onBlur", "");
		return json;
	}

	public static Node build(String key, String name, Options options) {
		Node node = new Node();
		node.setName(name);
		node.setModel(key);
		node.setKey(key);
		node.setOptions(options);
		return node;
	}
}
