package com.jeeplus.flsflow.config;

import com.jeeplus.flsflow.listener.GlobalTaskAssignmentListener;
import org.flowable.common.engine.api.delegate.event.FlowableEventListener;
import org.flowable.spring.SpringProcessEngineConfiguration;
import org.flowable.spring.boot.EngineConfigurationConfigurer;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.List;

@Configuration
public class FlowableGlobalListenerConfiguration implements EngineConfigurationConfigurer<SpringProcessEngineConfiguration> {

    private final GlobalTaskAssignmentListener globalTaskAssignmentListener;

    public FlowableGlobalListenerConfiguration(GlobalTaskAssignmentListener globalTaskAssignmentListener) {
        this.globalTaskAssignmentListener = globalTaskAssignmentListener;
    }

    @Override
    public void configure(SpringProcessEngineConfiguration engineConfiguration) {
        // 添加全局事件监听器
        List<FlowableEventListener> eventListeners = engineConfiguration.getEventListeners();
        if (eventListeners == null) {
            eventListeners = new ArrayList<>();
            engineConfiguration.setEventListeners(eventListeners);
        }
        eventListeners.add(globalTaskAssignmentListener);
    }
}
