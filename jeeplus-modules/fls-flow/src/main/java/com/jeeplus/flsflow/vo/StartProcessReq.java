package com.jeeplus.flsflow.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * created 2023/11/7 22:38:18
 */
@Data
public class StartProcessReq {
    @ApiModelProperty(value = "流程id", required = true)
    @NotBlank
    private String workflowId;

    @ApiModelProperty("流程标题, *在*时间发起了*流程")
    private String title;

    @ApiModelProperty(value = "流程表单入参,JSON格式", required = true)
    @NotNull(message = "表单入参不能为空")
    private Object data;

    @ApiModelProperty("审批过程监听,回调地址")
    private String listenerUrl;
}
