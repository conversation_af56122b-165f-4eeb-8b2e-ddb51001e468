package com.jeeplus.flsflow.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Set;

/**
 * <AUTHOR>
 * created 2023/11/10 15:21:41
 */
@Data
public class WorkflowCreateReq {
    @ApiModelProperty("流程名称")
    @NotBlank(message = "名称不能为空")
    private String name;

    @NotBlank(message = "流程类型不能为空")
    @ApiModelProperty(value = "流程类型", required = true)
    private String workflowType;

    @NotNull(message = "状态不能为空")
    @ApiModelProperty(value = "1-启用,2-禁用", required = true)
    private Integer status;

    @ApiModelProperty(value = "条件类型:1-组织, 默认:1", required = true)
    private Integer conditionType = 1;

    @ApiModelProperty("条件业务id")
    @NotEmpty(message = "条件业务id值不能为空")
    private Set<String> bizIdList;
}
