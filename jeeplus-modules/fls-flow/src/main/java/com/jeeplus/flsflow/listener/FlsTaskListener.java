package com.jeeplus.flsflow.listener;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson2.JSON;
import com.jeeplus.extension.domain.TaskDefExtension;
import com.jeeplus.extension.service.TaskDefExtensionService;
import com.jeeplus.extension.service.dto.FlowAssigneeDTO;
import com.jeeplus.flowable.model.TaskComment;
import com.jeeplus.flowable.service.FlowTaskService;
import com.jeeplus.flsflow.domain.CandidateTypeDto;
import com.jeeplus.flsflow.domain.TaskEvent;
import com.jeeplus.flsflow.domain.WorkflowInstance;
import com.jeeplus.flsflow.enums.AssigneeType;
import lombok.extern.slf4j.Slf4j;
import org.flowable.bpmn.model.UserTask;
import org.flowable.engine.HistoryService;
import org.flowable.engine.TaskService;
import org.flowable.engine.delegate.TaskListener;
import org.flowable.engine.impl.util.ProcessDefinitionUtil;
import org.flowable.identitylink.api.IdentityLinkInfo;
import org.flowable.task.api.Task;
import org.flowable.task.api.TaskQuery;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.flowable.task.service.delegate.DelegateTask;
import org.flowable.task.service.impl.persistence.entity.TaskEntityImpl;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * created 2023/11/15 23:44:22
 */
@Component
@Slf4j
public class FlsTaskListener extends AbstractListener implements TaskListener {

    @Override
    public void notify(DelegateTask delegateTask) {
        FlowTaskService flowTaskService = SpringUtil.getBean(FlowTaskService.class);
        List<TaskComment> taskComments = new ArrayList<>(0);
        if ("complete".equals(delegateTask.getEventName())) {
            taskComments = flowTaskService.getTaskComments(delegateTask.getId());
        }else if ("create".equals(delegateTask.getEventName())){
           taskComments = getLastComments(delegateTask.getProcessInstanceId());
        }
        List<String> candidateIds = new ArrayList<>(0);
        if (!ObjectUtils.isEmpty(delegateTask.getCandidates())){
            candidateIds = delegateTask.getCandidates().stream().map(IdentityLinkInfo::getUserId).collect(Collectors.toList());
        }else {
            candidateIds.add(delegateTask.getAssignee());
        }
        TaskEvent taskEvent = TaskEvent.builder().event(delegateTask.getEventName())
                .taskId(delegateTask.getId())
                .processInsId(delegateTask.getProcessInstanceId())
                .operatorId(delegateTask.getAssignee())
                .data(delegateTask.getVariables())
                .taskName(delegateTask.getName())
                .commentList(taskComments)
                .eventTime(new Date().getTime())
                .candidateIds(candidateIds)
                .build();
        log.info("{}:{}", delegateTask.getEventName(), JSON.toJSONString(taskEvent));
        doTaskCallback(taskEvent);
    }

    private List <TaskComment> getLastComments(String processInsId){
        HistoryService historyService = SpringUtil.getBean(HistoryService.class);
        List<HistoricTaskInstance> taskInstanceList = historyService
                .createHistoricTaskInstanceQuery ( )
                .processInstanceId ( processInsId )
                .orderByTaskCreateTime ( )
                .desc ( ).listPage(0,1);
        if (ObjectUtils.isEmpty(taskInstanceList)) {
            return new ArrayList<>(0);
        }
        //查询最后一个任务的执行审批结果
        FlowTaskService flowTaskService = SpringUtil.getBean(FlowTaskService.class);
        return flowTaskService.getTaskComments(taskInstanceList.get(0).getId());
    }
}
