package com.jeeplus.flsflow.service.impl;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jeeplus.extension.domain.BillMetadata;
import com.jeeplus.extension.domain.FormCategory;
import com.jeeplus.extension.domain.FormDefinition;
import com.jeeplus.extension.domain.FormDefinitionJson;
import com.jeeplus.extension.mapper.BillMetadataMapper;
import com.jeeplus.extension.mapper.FormCategoryMapper;
import com.jeeplus.extension.service.FormDefinitionJsonService;
import com.jeeplus.extension.service.FormDefinitionService;
import com.jeeplus.flsflow.exception.WorkflowException;
import com.jeeplus.flsflow.service.BillResourceService;
import com.jeeplus.flsflow.vo.BillResourceVo;
import com.jeeplus.flsflow.vo.form.Config;
import com.jeeplus.flsflow.vo.form.FormJsonVo;
import com.jeeplus.flsflow.vo.form.Node;
import com.jeeplus.flsflow.vo.form.Options;
import com.jeeplus.sys.constant.CommonConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 单据资源服务实现类
 *
 * <AUTHOR>
 *
 */
@Service
public class BillResourceServiceImpl extends ServiceImpl<BillMetadataMapper, BillMetadata> implements BillResourceService {
	@Autowired
	private BillMetadataMapper billMetadataMapper;

	@Autowired
	private FormCategoryMapper formCategoryMapper;

	@Autowired
	private FormDefinitionService formDefinitionService;

	@Autowired
	private FormDefinitionJsonService formDefinitionJsonService;

	@Override
	@Transactional
	public void init(BillResourceVo billResource) {
		//1、保存表单数据
		String formId = this.saveForm(billResource);

		//2、保存表单json数据
		FormDefinitionJson formDefinitionJson = formDefinitionJsonService.lambdaQuery()
				.eq(FormDefinitionJson::getFormDefinitionId, formId)
				.eq(FormDefinitionJson::getIsPrimary, CommonConstants.YES).one();
		if (ObjectUtils.isEmpty(formDefinitionJson)) {
			formDefinitionJson = new FormDefinitionJson();
			formDefinitionJson.setFormDefinitionId(formId);
			formDefinitionJson.setVersion(1);
			formDefinitionJson.setStatus(CommonConstants.YES);
			formDefinitionJson.setJson(this.parseFormJson(billResource.getFormdata()));
			formDefinitionJson.setIsPrimary(CommonConstants.YES);
			formDefinitionJsonService.save(formDefinitionJson);
		} else {
			formDefinitionJson.setId("");// 发布新版本
			formDefinitionJson.setVersion(formDefinitionJsonService.getMaxVersion(formDefinitionJson) + 1);
			formDefinitionJson.setJson(this.parseFormJson(billResource.getFormdata()));
			formDefinitionJson.setFormDefinitionId(formId);
			formDefinitionJson.setStatus(CommonConstants.YES);
			formDefinitionJsonService.saveOrUpdate(formDefinitionJson);// 保存
			formDefinitionJsonService.updatePrimary(formDefinitionJson.getId());// 设置为新版本
		}

		//3、保存单据资源元数据
		QueryWrapper<BillMetadata> billMetadataQueryWrapper = new QueryWrapper<BillMetadata>();
		billMetadataQueryWrapper.eq("id_resource", billResource.getIdResource())
				.eq("del_flag", CommonConstants.NOT_DELETED);
		BillMetadata billMetadata = billMetadataMapper.selectOne(billMetadataQueryWrapper);
		String metadata = billResource.getMetadata().toString();
		String formdata = billResource.getFormdata().toString();
		String primaryKey = billResource.getMetadata().getJSONObject("data").getString("primaryKey");
		String bizunitKey = billResource.getMetadata().getJSONObject("data").getString("bizunitKey");
		if (ObjectUtils.isEmpty(billMetadata)) {
			billMetadata = new BillMetadata();
			billMetadata.setIdResource(billResource.getIdResource());
			billMetadata.setMetadata(metadata);
			billMetadata.setBillCode(billResource.getBillCode());
			billMetadata.setBillName(billResource.getBillName());
			billMetadata.setFormId(formId);
			billMetadata.setFormdata(formdata);
			billMetadata.setDefaultLimitType(billResource.getDefaultLimitType());
			billMetadata.setDefaultLimitVal(billResource.getDefaultLimitVal());
			billMetadata.setPrimaryKey(primaryKey);
			billMetadata.setBizunitKey(bizunitKey);
			billMetadataMapper.insert(billMetadata);
		} else {
			billMetadata.setBillName(billResource.getBillName());
			billMetadata.setDefaultLimitType(billResource.getDefaultLimitType());
			billMetadata.setDefaultLimitVal(billResource.getDefaultLimitVal());
			billMetadata.setMetadata(metadata);
			billMetadata.setFormdata(formdata);
			billMetadata.setFormId(formId);
			billMetadata.setPrimaryKey(primaryKey);
			billMetadata.setBizunitKey(bizunitKey);
			billMetadataMapper.updateById(billMetadata);
		}

	}

	private String saveForm(BillResourceVo billResource) {
		// 保存表单信息 归类到名称为【单据表单】类别下
		FormDefinition formDefinition = formDefinitionService.lambdaQuery()
				.eq(FormDefinition::getName, billResource.getBillName())
				.eq(FormDefinition::getDelFlag, CommonConstants.NOT_DELETED).one();
		if (ObjectUtils.isEmpty(formDefinition)) {
			QueryWrapper<FormCategory> queryWrapper = new QueryWrapper<FormCategory>();
			queryWrapper.eq("name", "单据表单");
			FormCategory formCategory = formCategoryMapper.selectOne(queryWrapper);
			if (ObjectUtils.isEmpty(formCategory)) {
				throw new WorkflowException("表单类型【单据表单】不存在");
			}
			formDefinition = new FormDefinition();
			formDefinition.setName(billResource.getBillName());
			formDefinition.setCategoryId(formCategory.getId());
			formDefinitionService.save(formDefinition);
		}
		return formDefinition.getId();
	}

	private String parseFormJson(JSONArray formdata) {
		if (formdata == null || formdata.size() <= 0) {
			return "";
		}
		FormJsonVo formJson = new FormJsonVo();
		JSONArray list = new JSONArray();
		for (int i = 0; i < formdata.size(); i++) {
			JSONObject item = formdata.getJSONObject(i);
			String key = item.getString("key");
			String name = item.getString("name");
			Options options = Options.build(key);
			Node node = Node.build(key, name, options);
			list.add(node);
		}
		formJson.setList(list);
		formJson.setConfig(Config.build());
		return JSONObject.toJSONString(formJson);
	}

	@Override
	public BillMetadata getBillMetadata(String billCode) {
		QueryWrapper<BillMetadata> billMetadataQueryWrapper = new QueryWrapper<BillMetadata>();
		billMetadataQueryWrapper.eq("bill_code", billCode).eq("del_flag", CommonConstants.NOT_DELETED);
		return billMetadataMapper.selectOne(billMetadataQueryWrapper);
	}

	@Override
	public Page<BillMetadata> page(Page<BillMetadata> page, BillMetadata billMetadata) {
		return this.lambdaQuery()
				.like(!ObjectUtils.isEmpty(billMetadata) && !ObjectUtils.isEmpty(billMetadata.getBillCode()),
						BillMetadata::getBillCode, billMetadata.getBillCode())
				.like(!ObjectUtils.isEmpty(billMetadata) && !ObjectUtils.isEmpty(billMetadata.getBillName()),
						BillMetadata::getBillName, billMetadata.getBillName())
				.orderByDesc(BillMetadata::getCreateTime).page(new Page<>(page.getCurrent(), page.getSize()));
	}

	@Override
	public List<String> getResourceIds() {
		List<BillMetadata> list = this.lambdaQuery().eq(BillMetadata::getDelFlag, CommonConstants.NOT_DELETED).ne(BillMetadata::getIdResource, "").list();
		if(ObjectUtils.isEmpty(list)){
			return new ArrayList<String>();
		}
		return list.stream().map(BillMetadata::getIdResource).collect(Collectors.toList());
	}
}
