package com.jeeplus.flsflow.enums;

public enum ProcessCodeEnum {
	DRAFT(0, "待提交"), 
	WAITING(1, "待审核"), 
	COMPLETED(2, "已完成"), 
	REVOKE(3, "已撤回"), 
	REJECT(4, "已驳回"), 
	VOIDED(5, "已作废"),
	STOP(6, "不同意");

	private final int code;

	private final String name;

	ProcessCodeEnum(int code, String name) {
		this.name = name;
		this.code = code;
	}

	public String getName() {
		return name;
	}

	public int getCode() {
		return code;
	}

	public static ProcessCodeEnum getEnumByCode(Integer code) {
		if (code == null) {
			return null;
		}
		for (ProcessCodeEnum processCodeEnum : ProcessCodeEnum.values()) {
			if (processCodeEnum.getCode() == code.intValue()) {
				return processCodeEnum;
			}
		}
		return null;
	}
}
