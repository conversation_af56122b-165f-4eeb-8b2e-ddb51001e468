package com.jeeplus.flsflow.enums;

/**
 * <AUTHOR>
 * created 2023/11/10 15:47:09
 */
public enum WorkflowConditionType {
    /**
     * 组织
     */
    ORG(1);

    private final int value;

    WorkflowConditionType(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public static WorkflowConditionType fromValue(int value){
        for (WorkflowConditionType workflowConditionType : WorkflowConditionType.values()) {
            if (workflowConditionType.getValue() == value){
                return workflowConditionType;
            }
        }
        throw new RuntimeException("类型未定义");
    }
}
