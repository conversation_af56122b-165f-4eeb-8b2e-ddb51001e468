package com.jeeplus.flsflow.exception;

/**
 * <AUTHOR>
 * created 2023/12/15 19:04:21
 */
public class WorkflowException extends RuntimeException{
    private final String msg;
    private final Integer code;

    public WorkflowException(String msg){
        this.msg = msg;
        this.code = -1;
    }

    public WorkflowException(String msg, Integer code){
        super(msg);
        this.msg = msg;
        this.code = code;
    }

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
