package com.jeeplus.flsflow;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jeeplus.aop.logging.annotation.ApiLog;
import com.jeeplus.flowable.controller.FlowableModelController;
import com.jeeplus.flowable.model.FlowModel;
import com.jeeplus.flowable.service.FlowProcessService;
import com.jeeplus.flowable.service.FlowableModelService;
import com.jeeplus.fls.nc.api.domain.BaseBizunit;
import com.jeeplus.fls.nc.api.service.BaseBizunitService;
import com.jeeplus.flsflow.domain.WorkflowModel;
import com.jeeplus.flsflow.enums.WorkflowConditionType;
import com.jeeplus.flsflow.exception.WorkflowException;
import com.jeeplus.flsflow.service.WorkflowModelService;
import com.jeeplus.flsflow.utils.ResultUtil;
import com.jeeplus.flsflow.vo.*;
import com.jeeplus.sys.domain.DictType;
import com.jeeplus.sys.domain.DictValue;
import com.jeeplus.sys.service.DictTypeService;
import com.jeeplus.sys.service.DictValueService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * created 2023/11/8 17:13:28
 */
@Api(tags = "流程管理")
@RestController
public class FlsFlowManageController {

    @Resource
    private DictValueService dictValueService;

    @Resource
    private DictTypeService dictTypeService;

    @Resource
    private WorkflowModelService workflowModelService;

    @Resource
    private FlowableModelController flowableModelController;

    @Resource
    private FlowableModelService flowableModelService;

    @Resource
    private FlowProcessService flowProcessService;

    @Resource
    private BaseBizunitService baseBizunitService;

    @ApiOperation("工作流业务类型列表查询")
    @PostMapping("/workflow/typeList")
    public BaseResult<List<WorkflowTypeVo>> workflowType(@RequestBody KeywordReq req) {
        DictType dictType = dictTypeService.lambdaQuery().eq(DictType::getType, "workflow_type").one();
        if (ObjectUtils.isEmpty(dictType)) {
            throw new RuntimeException("未配置字典:workflow_type");
        }
        List<DictValue> dictValues = dictValueService.lambdaQuery()
                .eq(DictValue::getDictTypeId, dictType.getId())
                .like(!ObjectUtils.isEmpty(req.getKeyword()), DictValue::getLabel, req.getKeyword())
                .orderByAsc(DictValue::getSort)
                .list();

        List<WorkflowTypeVo> workflowTypeVoList = dictValues.stream().map(it -> {
            WorkflowTypeVo typeVo = new WorkflowTypeVo();
            typeVo.setName(it.getLabel());
            typeVo.setType(it.getValue());
            return typeVo;
        }).collect(Collectors.toList());

        return ResultUtil.ok(workflowTypeVoList);
    }

    @ApiOperation("工作流适用条件列表")
    @PostMapping("/workflow/conditionList")
    public BaseResult<List<WorkConditionVo>> queryConditionList(@RequestBody ConditionTypeReq req){
        WorkflowConditionType conditionType = WorkflowConditionType.fromValue(req.getType());

        Map<String, String> resMap = workflowModelService.queryBizInfo(conditionType, Collections.emptySet());
        List<WorkConditionVo> conditionVoList =  resMap.keySet().stream().map(it->{
            WorkConditionVo conditionVo = new WorkConditionVo();
            conditionVo.setBizId(it);
            conditionVo.setBizName(resMap.get(it));
            return conditionVo;
        }).collect(Collectors.toList());
        return ResultUtil.ok(conditionVoList);
    }

    @ApiLog("工作流创建")
    @ApiOperation("工作流创建")
    @PostMapping("/workflow/create")
    public BaseResult<WorkflowModel> workflowCreate(@RequestBody @Valid WorkflowCreateReq req) {
        return ResultUtil.ok(workflowModelService.create(req));
    }

    @ApiLog("工作流删除")
    @PostMapping("/workflow/delete")
    public BaseResult<WorkflowModel> workflowDelete(@RequestBody @Valid IdReq req) {
        return ResultUtil.ok(workflowModelService.delete(req));
    }

    @ApiLog("工作流解绑")
    @ApiOperation("工作流解绑")
    @PostMapping("/workflow/unbind")
    public BaseResult<WorkflowModel> workflowUnBind(@RequestBody @Valid IdReq req) {
        workflowModelService.unbind(req);
        return ResultUtil.ok("suc");
    }

    @ApiLog("工作流编辑")
    @ApiOperation("工作流编辑")
    @PostMapping("/workflow/update")
    public BaseResult<Boolean> workflowCreate(@RequestBody @Valid WorkflowUpdateReq req) {
        try {
            return ResultUtil.ok(workflowModelService.update(req));
        }catch (WorkflowException e){
            return ResultUtil.error(e.getMsg());
        }
    }

    @ApiLog("流程绑定")
    @ApiOperation("流程绑定")
    @PostMapping("/workflow/model/bind")
    public BaseResult<WorkflowModel> workflowModelDesign(@RequestBody @Valid WorkflowBindReq req) {
        WorkflowModel workflowModel = workflowModelService.bindFlowableModelId(req.getFlowableModelId(), req.getWorkflowModelId());
        return ResultUtil.ok(workflowModel);
    }

    @ApiOperation("工作流列表查询")
    @PostMapping("/workflow/list")
    public BaseResult<Page<WorkflowModelVo>> workflowList(@RequestBody WorkflowPageReq req){
        return ResultUtil.ok(workflowModelService.list(req));
    }

    @ApiOperation("流程模型列表查询,查询条件:list?current=1&size=10&filterText=关键词")
    @GetMapping("/workflow/flowableModel/list")
    public BaseResult<Page<FlowModel>> flowableModelList(Page <FlowModel> page, HttpServletRequest request) {
        Page <FlowModel> result = flowableModelService.getModels ( page, "processes", "modifiedDesc", 0, request );
        return ResultUtil.ok(result);
    }

//    @ApiLog("挂起、激活流程实例")
//    @ApiOperation("挂起、激活流程实例")
//    @PostMapping("/workflow/enable")
//    public BaseResult<String> updateState(@RequestBody @Valid EnableReq enableReq) {
//        ProcessDefinition processDefinition = flowProcessService.getProcessDefinitionByKey(enableReq.getKey());
//        String message = flowProcessService.updateState ( enableReq.getStatus(), processDefinition.getId() );
//        return ResultUtil.ok ( message );
//    }
//
//    @ApiLog("发布")
//    @ApiOperation("发布")
//    @PostMapping("/workflow/deploy")
//    public BaseResult<String> deploy(@RequestBody @Valid IdReq idReq) {
//        String result = flowableModelService.deploy ( idReq.getId(), null, null );
//        return ResultUtil.ok ( result );
//    }
}
