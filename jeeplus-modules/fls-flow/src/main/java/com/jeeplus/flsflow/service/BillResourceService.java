package com.jeeplus.flsflow.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jeeplus.extension.domain.BillMetadata;
import com.jeeplus.flsflow.vo.BillResourceVo;

/**
 * 单据资源服务接口
 *
 * <AUTHOR>
 *
 */
public interface BillResourceService {
	/**
	 * 注册初始化单据资源
	 * 
	 * @param billResource
	 */
	void init(BillResourceVo billResource);
	
	/**
	 * 获取元数据
	 *
	 * @param billCode 单据资源编码
	 * @return
	 */
	BillMetadata getBillMetadata(String billCode);
	
	Page<BillMetadata> page(Page<BillMetadata> page, BillMetadata billMetadata);
	
	List<String> getResourceIds();
}
