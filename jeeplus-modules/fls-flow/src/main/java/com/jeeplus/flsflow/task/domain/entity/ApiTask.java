package com.jeeplus.flsflow.task.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * created 2023/11/12 22:15:02
 */
@Data
@TableName("t_api_task")
public class ApiTask {
    @TableId(type = IdType.ASSIGN_UUID)
    private String idApiTask;
    /**
     * http方法
     */
    private String method;
    /**
     * 执行状态
     */
    private String taskStatus;

    /**
     * 接口地址
     */
    private String requestUrl;

    /**
     * 请求参数
     */
    private String requestData;

    /**
     * 参数构建代理
     */
    private String argBuilder;

    /**
     * 响应结果
     */
    private String responseData;

    /**
     * 响应结果备注:执行成功，失败具体原因
     */
    private String responseRemark;

    /**
     * 最近一次执行时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime lastExecuteTime;

    /**
     * 执行次数
     */
    private Integer executeCount;

    /**
     * 来源单据id
     */
    private String idSource;
    /**
     * 来源单据编码
     */
    private String codeSource;

    /**
     * 来源单据类型
     */
    private String sourceType;

    private String creator;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime createTime;

    private String deleteFlag;
}
