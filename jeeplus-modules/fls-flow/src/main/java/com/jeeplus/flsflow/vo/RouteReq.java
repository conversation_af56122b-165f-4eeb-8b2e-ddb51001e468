package com.jeeplus.flsflow.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * created 2023/11/16 10:47:59
 */
@Data
public class RouteReq {
    @NotBlank(message = "流程类型不能为空")
    @ApiModelProperty("流程类型")
    private String workflowType;

    @NotNull(message = "条件类型")
    @ApiModelProperty("条件类型")
    private Integer conditionType;

    @NotBlank(message = "条件业务id不能为空")
    @ApiModelProperty("业务id")
    private String bizId;
}
