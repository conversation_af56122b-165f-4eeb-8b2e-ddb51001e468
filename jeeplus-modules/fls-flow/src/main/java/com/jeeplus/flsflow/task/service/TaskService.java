package com.jeeplus.flsflow.task.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jeeplus.flsflow.task.domain.ApiTaskReq;
import com.jeeplus.flsflow.task.domain.TaskPageQuery;
import com.jeeplus.flsflow.task.domain.TaskQuery;
import com.jeeplus.flsflow.task.domain.entity.ApiTask;

import java.util.concurrent.Future;

/**
 * <AUTHOR>
 * created 2023/11/12 10:45:31
 */
public interface TaskService {
    /**
     * 添加任务
     */
    String addTask(ApiTaskReq apiTaskReq, String arguments);

    /**
     * 添加任务
     */
    ApiTask addTask(ApiTaskReq apiTaskReq, ArgBuilder delegate);

    /**
     * 同步触发任务
     */
    ApiTask target(TaskQuery taskQuery, TaskResultParser parser);

    /**
     * 异步出发任务
     */
    Future<ApiTask> asyncTarget(TaskQuery taskQuery, TaskResultParser parser);

    Page<ApiTask> query(TaskPageQuery pageQuery);

    ApiTask queryOneTask(TaskQuery taskQuery);
}
