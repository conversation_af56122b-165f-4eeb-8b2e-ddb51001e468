package com.jeeplus.flsflow.task.domain;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.jeeplus.flsflow.task.enums.TaskMethod;
import com.jeeplus.flsflow.task.enums.TaskMethodConverter;
import lombok.Data;

/**
 * <AUTHOR>
 * created 2023/11/12 10:48:54
 */
@Data
public class ApiTaskReq {
    /**
     * 接口地址
     */
    private String requestUrl;
    /**
     * http方法
     */
    @JsonDeserialize(using = TaskMethodConverter.class)
    private TaskMethod method;

    /**
     * 来源单据id
     */
    private String idSource;
    /**
     * 来源单据号
     */
    private String codeSource;

    /**
     * 来源单据类型
     */
    private String sourceType;
}
