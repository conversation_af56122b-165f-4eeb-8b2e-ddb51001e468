package com.jeeplus.flsflow.task.http;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.jeeplus.security.util.UserSessionDto;
import com.jeeplus.security.util.UserSessionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/2
 * @since 1.0.0
 */
@Component
@Slf4j
public class RestHttpTemplate {
    @Resource
    private RestTemplate restTemplate;

    public String get(Request request) {
        String url = String.format("%s?%s", request.getUrl(), buildGetArgs(request.getArgs()));
        log.info("request:" + url);
        ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);
        log.info("response:" + response);
        if (!HttpStatus.OK.equals(response.getStatusCode())) {
            throw new HttpException(response.getStatusCode().value(), "调用服务接口失败");
        }
        return response.getBody();
    }

    private String buildGetArgs(Object args) {
        JSONObject jsonObject = JSONUtil.parseObj(JSONUtil.toJsonStr(args));
//        JSONObject jsonObject = JSON.parseObject(object);
        List<String> coupleKeys = new ArrayList<>(0);
        for (String key : jsonObject.keySet()) {
            coupleKeys.add(String.format("%s=%s", key, jsonObject.get(key)));
        }
        return String.join("&", coupleKeys);
    }

    public String post(Request request) {
        log.info("request:" + JSONUtil.toJsonStr(request));
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        UserSessionDto sessionDto = UserSessionUtils.getCurrent();
        headers.set("token",sessionDto.getToken());
        headers.set("client-source",sessionDto.getClientSource());
        HttpEntity<String> entity = new HttpEntity<>(JSONUtil.toJsonStr(request.getArgs()), headers);
        String response = restTemplate.postForObject(request.getUrl(), entity, String.class);
        log.info("response:" + response);
        return response;
    }
}
