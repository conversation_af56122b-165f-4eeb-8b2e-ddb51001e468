package com.jeeplus.flsflow.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jeeplus.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * created 2023/11/10 15:02:02
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("t_workflow_condition")
public class WorkflowCondition extends BaseEntity {
    /**
     *  char(36) NOT NULL COMMENT '流程id'
     */
    private String workflowModelId;
    /**
     *  int NOT NULL COMMENT '条件类型:1-经营主体'
     */
    private Integer conditionType;
    /**
     *  varchar(1024) NOT NULL COMMENT '条件对应业务id'
     */
    private String bizId;
    private String bizName;
}
