package com.jeeplus.flsflow.vo;

import java.util.Date;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jeeplus.flowable.vo.ProcessVo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class BillFlowTodoReq extends Page<ProcessVo>{
	private static final long serialVersionUID = 1L;

	@ApiModelProperty("单据编码")
	private String billCode;
	
	@ApiModelProperty("交易类型编码")
	private String transTypeCode;
	
	@ApiModelProperty("开始查询日期")
	private Date beginDate;
	
	@ApiModelProperty("结束查询日期")
    private Date endDate;
	
	@ApiModelProperty("标题")
	private String title;
}
