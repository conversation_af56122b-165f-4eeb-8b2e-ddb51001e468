package com.jeeplus.flsflow.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jeeplus.flsflow.domain.WorkflowModel;
import com.jeeplus.flsflow.vo.RouteReq;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * created 2023/11/10 15:05:16
 */
@Mapper
public interface WorkflowModelMapper extends BaseMapper<WorkflowModel> {
    WorkflowModel routWorkflow(@Param("route") RouteReq req);
}
