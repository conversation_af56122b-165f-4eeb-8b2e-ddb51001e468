package com.jeeplus.flsflow.vo;

import com.jeeplus.flowable.model.Flow;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * created 2023/12/21 11:37:21
 */
@Data
@ApiModel("审批流节点及审批历史")
public class FlowVo {
    @ApiModelProperty("审批历史记录")
    private List<Flow> historyFlowList;
    @ApiModelProperty("审批流节点")
    private List<FlowNodeVo> flowNodeList;
}
