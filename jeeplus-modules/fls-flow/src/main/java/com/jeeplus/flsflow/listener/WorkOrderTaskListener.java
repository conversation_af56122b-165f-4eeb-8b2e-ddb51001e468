package com.jeeplus.flsflow.listener;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.jeeplus.flsflow.constant.MessageMqConstant;
import com.jeeplus.flsflow.domain.OrderTaskMessage;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.delegate.TaskListener;
import org.flowable.identitylink.api.IdentityLink;
import org.flowable.task.service.delegate.DelegateTask;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.stream.Collectors;

/**
 * 流程任务监听器，用于发送消息到消息队列
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
@Slf4j
@Component("workOrderTaskListener")
public class WorkOrderTaskListener implements TaskListener {

    @Resource
    private RabbitTemplate rabbitTemplate;

    @Override
    public void notify(DelegateTask delegateTask) {
        //提取message属性
        String candidates = StrUtil.join(",", delegateTask.getCandidates().stream().map(IdentityLink::getUserId).collect(Collectors.toList()));
        String assignee = delegateTask.getAssignee();
        String processInstanceId = delegateTask.getProcessInstanceId();
        String taskId = delegateTask.getId();
        //去掉Resource_
        String taskDefinitionKey = delegateTask.getTaskDefinitionKey();
        if (StrUtil.isNotBlank(taskDefinitionKey) && taskDefinitionKey.startsWith("Resource_")) {
            taskDefinitionKey = StrUtil.removePrefix(taskDefinitionKey, "Resource_");
        }
        OrderTaskMessage message = OrderTaskMessage.builder()
                .eventName(delegateTask.getEventName())
                .idTaskDefinition(taskDefinitionKey)
                .idProcInst(processInstanceId)
                .idProcTask(taskId)
                .candidates(candidates)
                .assignees(assignee)
                .build();
        try {
            //发送消息到消息队列
            rabbitTemplate.convertAndSend(MessageMqConstant.WORKORDER_TASK_EXCHANGE, MessageMqConstant.WORKORDER_TASK_TOPIC, JSONUtil.toJsonStr(message),
                    new CorrelationData(taskId));
            //日志打点
            log.info("success send task message to mq: {}", JSONUtil.toJsonStr(message));
        }
        catch (Exception e) {
            log.error("failed to send todo message, error:{},message:{}", e.getMessage(), JSONUtil.toJsonStr(message));
        }
    }


}
