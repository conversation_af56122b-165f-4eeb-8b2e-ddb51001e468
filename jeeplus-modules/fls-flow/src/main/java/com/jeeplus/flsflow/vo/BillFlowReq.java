package com.jeeplus.flsflow.vo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 单据流程请求参数对象
 *
 * <AUTHOR>
 *
 */
@Data
public class BillFlowReq {
	
	@ApiModelProperty("单据编码")
    @NotBlank(message = "单据编码不能为空")
	private String billCode;
	
	@ApiModelProperty("交易类型编码")
	private String transTypeCode;

    @ApiModelProperty("流程标题, *在*时间发起了*流程")
    private String title;

    @ApiModelProperty(value = "流程表单入参,JSON格式", required = true)
    @NotNull(message = "表单入参不能为空")
    private Object data;
    
    @ApiModelProperty("是否向上查找")
    private boolean findHead;
}
