package com.jeeplus.flsflow.listener;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson2.JSON;
import com.jeeplus.flowable.constant.FlowableConstant;
import com.jeeplus.flowable.model.TaskComment;
import com.jeeplus.flowable.service.FlowProcessService;
import com.jeeplus.flowable.service.FlowTaskService;
import com.jeeplus.flowable.vo.ActionType;
import com.jeeplus.flowable.vo.ProcessStatus;
import com.jeeplus.flowable.vo.ProcessVo;
import com.jeeplus.flsflow.domain.TaskEvent;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.HistoryService;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.ExecutionListener;
import org.flowable.engine.history.HistoricActivityInstance;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.flowable.variable.api.history.HistoricVariableInstance;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * created 2023/11/15 23:44:22
 */
@Component
@Slf4j
public class FlsExecutionListener extends AbstractListener implements ExecutionListener {

    @Override
    public void notify(DelegateExecution execution) {
        HistoryService historyService = SpringUtil.getBean(HistoryService.class);
        HistoricTaskInstance historicActivityInstances = historyService
                .createHistoricTaskInstanceQuery ( )
                .processInstanceId ( execution.getProcessInstanceId() )
                .orderByTaskCreateTime ( )
                .desc ( ).listPage(0,1).get(0);
        //查询最后一个任务的执行审批结果
        FlowTaskService flowTaskService = SpringUtil.getBean(FlowTaskService.class);
        List <TaskComment> taskComments = flowTaskService.getTaskComments(historicActivityInstances.getId());

        String operatorId = !ObjectUtils.isEmpty(historicActivityInstances.getAssignee()) ? historicActivityInstances.getAssignee():
                historicActivityInstances.getOwner();
        String eventName = execution.getEventName();
        TaskEvent taskEvent = TaskEvent.builder()
                .event(eventName)
                .operatorId(operatorId)
                .processInsId(execution.getProcessInstanceId())
                .commentList(taskComments)
                .data(execution.getVariables())
                .eventTime(new Date().getTime())
                .build();
        String taskJson = JSON.toJSONString(taskEvent);
        log.info("{}:{}", eventName, taskJson);
        doExecuteCallback(taskEvent);
    }
}
