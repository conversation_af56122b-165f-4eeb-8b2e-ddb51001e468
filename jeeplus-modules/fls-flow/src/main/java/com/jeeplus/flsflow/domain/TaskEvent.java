package com.jeeplus.flsflow.domain;

import com.jeeplus.flowable.model.TaskComment;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * created 2023/11/30 11:59:50
 */
@Data
@Builder
public class TaskEvent {
    private String taskId;

    private String taskName;

    private String processInsId;
    /**
     * 分配：assignment
     * 完成：complete
     * 结束：end
     * 结束：create
     */
    private String event;
    /**
     * 事件相关用户id
     */
    private String operatorId;

    /**
     * 任务执行候选人ids
     */
    private List<String> candidateIds;
    private Integer totalStep;
    private Integer currentStep;
    private List<TaskComment> commentList;

    private Map<String, Object> data;

    private String processStatus;
    private Long eventTime;
}
