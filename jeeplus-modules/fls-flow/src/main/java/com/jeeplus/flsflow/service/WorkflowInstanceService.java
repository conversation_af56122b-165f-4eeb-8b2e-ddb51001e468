package com.jeeplus.flsflow.service;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jeeplus.flowable.controller.FlowableFormController;
import com.jeeplus.flowable.service.FlowProcessService;
import com.jeeplus.flsflow.domain.WorkflowInstance;
import com.jeeplus.flsflow.domain.WorkflowModel;
import com.jeeplus.flsflow.mapper.WorkflowInstanceMapper;
import com.jeeplus.flsflow.vo.StartProcessReq;
import com.jeeplus.sys.utils.UserUtils;
import org.flowable.engine.repository.ProcessDefinition;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * created 2023/11/13 10:47:35
 */
@Service
public class WorkflowInstanceService extends ServiceImpl<WorkflowInstanceMapper, WorkflowInstance> {

    @Resource
    private FlowableFormController flowableFormController;

    @Resource
    private WorkflowModelService workflowModelService;

    @Resource
    private FlowProcessService flowProcessService;


    public String saveInstance(StartProcessReq req){
        if (ObjectUtils.isEmpty(req.getTitle())){
            req.setTitle(String.format("%s 在 %s 发起流程", UserUtils.getCurrentUserDTO().getName(), LocalDateTime.now()));
        }
        WorkflowModel workflowModel = workflowModelService.getById(req.getWorkflowId());
        if (ObjectUtils.isEmpty(workflowModel)){
            throw new RuntimeException("流程不存在");
        }
        ProcessDefinition processDefinition = flowProcessService.getProcessDefinitionByKey(workflowModel.getFlowableModelKey());
        ResponseEntity<String> entity = flowableFormController.submitStartFormData(null,
                processDefinition.getId(),
                req.getTitle(),
                JSON.toJSONString(req.getData()));

        WorkflowInstance instance = new WorkflowInstance();
        instance.setFlowableInsId(entity.getBody());
        instance.setFlowableModelId(req.getWorkflowId());
        instance.setFlowableModelKey(workflowModel.getFlowableModelKey());
        instance.setListenerUrl(req.getListenerUrl());
        this.save(instance);
        return instance.getFlowableInsId();
    }
}
