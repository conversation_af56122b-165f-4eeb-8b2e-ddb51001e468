<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jeeplus.flsflow.mapper.WorkflowModelMapper">
    <select id="routWorkflow" resultType="com.jeeplus.flsflow.domain.WorkflowModel">
        SELECT twm.* FROM t_workflow_model twm
        INNER JOIN t_workflow_condition twc ON twm.id = twc.workflow_model_id
        WHERE
            twc.condition_type = #{route.conditionType}
            AND twc.biz_id = #{route.bizId}
            AND twm.workflow_type = #{route.workflowType}
            AND twm.status = 1
          <![CDATA[
          AND twm.flowable_model_key <> ''
          ]]>
        AND twm.del_flag = '0' AND twc.del_flag = '0'
    </select>
</mapper>
