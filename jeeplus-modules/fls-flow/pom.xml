<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>org.jeeplus</groupId>
        <artifactId>jeeplus-modules</artifactId>
        <version>9.0</version>
    </parent>
    <packaging>jar</packaging>

    <name>fls-flow</name>

    <modelVersion>4.0.0</modelVersion>
    <description>佛朗斯流程引擎接口适配</description>

    <artifactId>fls-flow</artifactId>

    <properties>

    </properties>

    <dependencies>
        <dependency>
            <groupId>org.jeeplus</groupId>
            <artifactId>jeeplus-flowable</artifactId>
            <version>9.0</version>
        </dependency>
        <dependency>
            <groupId>org.jeeplus</groupId>
            <artifactId>fls-nc-api</artifactId>
            <version>9.0</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-amqp</artifactId>
        </dependency>
    </dependencies>


</project>
