<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jeeplus.fls.nc.api.mapper.ProcRoleGroupMapper">
    <select id="getRolegroupByCode" resultType="com.jeeplus.fls.nc.api.domain.RoleGroup">
        select
            r.id_rolegroup as idRolegroup,
            r.code,
            r.name,
            r.status,
            r.delete_flag as deleteFlag
        from t_proc_rolegroup r
        where r.code = #{code} AND r.delete_flag = '0'
        AND r.status = '2'
    </select>

	<select id="getRoleIdsByGroupId" resultType="java.lang.String">
        select r.id_role
        from t_proc_rolegroup_ship r
        where r.id_rolegroup = #{roleGroupId} AND r.delete_flag = '0'
        AND r.status = '2'
    </select>
</mapper>
