<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jeeplus.fls.nc.api.mapper.BaseUserMapper">

    <select id="getUserInfoById" resultType="com.jeeplus.fls.nc.api.domain.SysLoginUser">
        select
            tbu.id_user as userId,
            tbu.name as userName,
            tbp.id_org as orgId,
            tbu.code as code,
            tbu.id_identity as idIdentity
        from
            t_base_user tbu
                left join t_base_person tbp on
                tbu.id_identity = tbp.id_person
        where tbu.id_user = #{idUser}
    </select>

    <select id="getUserInfoByCode" resultType="com.jeeplus.fls.nc.api.domain.SysLoginUser">
        select
            tbu.id_user as userId,
            tbu.name as userName,
            tbp.id_org as orgId,
            tbu.code as code,
            tbu.id_identity as idIdentity
        from
            t_base_user tbu
                left join t_base_person tbp on
                tbu.id_identity = tbp.id_person
        where tbu.code = #{code}
    </select>

    <select id="selectByRoles" resultType="com.jeeplus.fls.nc.api.domain.SysLoginUser">
        SELECT tbu.id_user     as userId,
               tbu.name        as userName,
               tbu.code        as code,
               tbu.id_identity as idIdentity
        FROM t_base_userrole tbur
        INNER JOIN t_base_user tbu ON tbur.id_user = tbu.id_user
        WHERE tbur.id_role in
          <foreach collection="idRoles" item="item" open="(" close=")" separator=",">
              #{item}
          </foreach>
    </select>
</mapper>
