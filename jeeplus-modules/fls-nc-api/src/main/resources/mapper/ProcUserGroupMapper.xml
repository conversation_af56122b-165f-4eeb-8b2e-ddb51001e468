<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jeeplus.fls.nc.api.mapper.ProcUserGroupMapper">
    <select id="getProcessUserGroupByCode" resultType="com.jeeplus.fls.nc.api.domain.UserGroup">
        select
            r.id_usergroup as idUsergroup,
            r.code,
            r.name,
            r.status,
            r.delete_flag as deleteFlag
        from t_proc_usergroup r
        where r.code = #{code} AND r.delete_flag = '0'
        AND r.status = '2'
    </select>

	<select id="getUserIdsByGroupId" resultType="java.lang.String">
        select r.id_user
        from t_proc_usergroup_ship r
        where r.id_usergroup = #{userGroupId} AND r.delete_flag = '0'
        AND r.status = '2'
    </select>
</mapper>
