<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
	namespace="com.jeeplus.fls.nc.api.mapper.BaseResourceMapper">
	<resultMap type="com.jeeplus.fls.nc.api.domain.BaseResource" id="ResourceMap">
		<id property="idResource" column="id_resource" />
		<result property="code" column="resourc_code" />
		<result property="name" column="resource_name" />
		<result property="idResourceclass" column="id_resourceclass" />
		<collection property="trantypeList" ofType="com.jeeplus.fls.nc.api.domain.BaseResTrantype">
			<id property="idResTrantype" column="id_res_trantype" />
			<result property="idResource" column="res_tran_id_resource" />
			<result property="tranCode" column="tran_code" />
			<result property="tranType" column="tran_type" />
			<result property="resourceCode" column="resource_code" />
			<result property="displayOrder" column="display_order" />
		</collection>
	</resultMap>
	<select id="getResourcesByClassId" resultMap="ResourceMap">
		SELECT
		tr.id_resource ,
		tr.code resourc_code,
		tr.`name` resource_name,
		tr.id_resourceclass,
		tt.id_res_trantype ,
		tt.id_resource res_tran_id_resource,
		tt.tran_code,
		tt.tran_type,
		tt.resource_code,
		tt.display_order
		FROM t_base_resource tr LEFT JOIN
		(SELECT * from t_base_res_trantype r1 WHERE r1.delete_flag = 0
		AND r1.`status` = 2 ) tt
		on tr.id_resource = tt.id_resource
		WHERE tr.delete_flag = 0
		AND tr.`status` = 2
		AND tr.id_resourceclass = #{idResourceclass}
		ORDER BY tr.`code` , tt.display_order
	</select>

</mapper>
