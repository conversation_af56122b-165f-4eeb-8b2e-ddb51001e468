package com.jeeplus.fls.nc.api.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jeeplus.fls.nc.api.domain.BaseUser;
import com.jeeplus.fls.nc.api.domain.SysLoginUser;
import com.jeeplus.fls.nc.api.mapper.BaseUserMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/8/28
 */
@Service
@DS("fls_db")
@Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
public class BaseUserService extends ServiceImpl<BaseUserMapper, BaseUser> {

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public SysLoginUser getUserInfoById(String idUser) {
        return this.baseMapper.getUserInfoById(idUser);
    }

    public SysLoginUser getUserInfoByCode(String code) {
        return this.baseMapper.getUserInfoByCode(code);
    }

    public List<SysLoginUser> getUserInfoByRoles(List<String> idRoles) {
        return this.baseMapper.selectByRoles(idRoles);
    }
}
