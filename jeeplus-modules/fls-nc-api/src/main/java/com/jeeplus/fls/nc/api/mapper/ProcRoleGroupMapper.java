package com.jeeplus.fls.nc.api.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jeeplus.fls.nc.api.domain.RoleGroup;

public interface ProcRoleGroupMapper extends BaseMapper<RoleGroup>{

	RoleGroup getRolegroupByCode(@Param("code") String code);
	
	List<String> getRoleIdsByGroupId(@Param("roleGroupId") String roleGroupId);
}
