package com.jeeplus.fls.nc.api.service;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.jeeplus.fls.nc.api.domain.BaseResource;
import com.jeeplus.fls.nc.api.mapper.BaseResourceMapper;

@Service
@DS("fls_db")
public class BaseResourceService {
	@Autowired
	private BaseResourceMapper baseResourceMapper;
	
	public List<BaseResource> getResourcesByClassId(String idResourceclass){
		return baseResourceMapper.getResourcesByClassId(idResourceclass);
	}
	
}
