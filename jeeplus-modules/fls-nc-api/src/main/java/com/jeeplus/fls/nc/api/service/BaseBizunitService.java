package com.jeeplus.fls.nc.api.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jeeplus.fls.nc.api.domain.BaseBizunit;
import com.jeeplus.fls.nc.api.mapper.BaseBizunitMapper;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * created 2023/11/13 21:55:06
 */
@Service
@DS("fls_db")
public class BaseBizunitService extends ServiceImpl<BaseBizunitMapper, BaseBizunit> {

    public List<BaseBizunit> queryList(Set<String> idList){
        return this.lambdaQuery()
                .eq(BaseBizunit::getDeleteFlag, "0")
                .eq(BaseBizunit::getStatus, "2")
                .in(!ObjectUtils.isEmpty(idList), BaseBizunit::getIdBizunit, idList)
                .list();
    }
}
