package com.jeeplus.fls.nc.api.domain;

import java.util.List;

import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

@Data
@TableName("t_base_resource")
public class BaseResource {
	private String idResource;
	private String code; // 编码
	private String name; // 名称
	private String idResourceclass; // 所属分类',
	private String moduleCode; // '业务模块代码',
	private String needprocFlag;// '是否启用审批流：0=否，1=是，默认1，参见yesorno',
	private String incalFlag;// '是否进入综合待审列表：0=否，1=是，默认1，参见yesorno',
	private String status;// '启用状态：1=未启用，2=已启用，3=已停用，默认1，参见basestatus',
	private String deleteFlag;// '是否删除：0=否，1=是，默认0，参见yesorno',
	private String needauthFlag;// '是否启用数据权限控制：0=否，1=是，默认1，参见yesorno',
	private String incalLink;// '资源审核页url地址',
	private String resourceType;// '类型，1=档案类，2=单据类',
	private String needdocFlag;// '是否启用资料管理，0=否，1=是，参见yesorno',
	private String needapplyFlag;// '是否启用申请，0=否，1=是，参见yesorno',
	private String archmanaFlag;// '是否启用档案数据管理，0=否，1=是，参见yesorno',
	private String historyFlag;// '是否启用历史记录，0=否，1=是，参见yesorno',
	private String appFlag;// '是否进入移动APP：0=否，1=是，默认0，参见yesorno',
	private String appLink;// '移动端动态路由url',
	private String domainFlag;// '域名判断标识',
	private String quoteFlag;// '资源资料是否开启引用',
	
	private List<BaseResTrantype> trantypeList;
}
