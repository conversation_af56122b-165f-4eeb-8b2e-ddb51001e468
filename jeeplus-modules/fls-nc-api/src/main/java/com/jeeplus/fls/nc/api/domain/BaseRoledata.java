package com.jeeplus.fls.nc.api.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * <AUTHOR>
 * created 2023/12/22 10:59:22
 */
@Data
@TableName("t_base_roledata")
public class BaseRoledata {
    /**
     *  varchar(36) NOT NULL COMMENT '主键',
     */
    private String idRoledata;
    /**
     *  varchar(36) NOT NULL COMMENT '所属组织',
     */
    private String idOrg;
    /**
     *  varchar(36) DEFAULT NULL COMMENT '所属部门',
     */
    private String idDepartment;
    /**
     *  varchar(36) DEFAULT NULL COMMENT '所属岗位序列',
     */
    private String idPostseries;
    /**
     *  varchar(36) DEFAULT NULL COMMENT '所属岗位',
     */
    private String idPost;
    /**
     *  varchar(36) NOT NULL COMMENT '角色id',
     */
    private String idRole;
    /**
     *  char(1) DEFAULT '1' COMMENT '启用状态：1=未启用，2=已启用，3=已停用，默认1，参见basestatus',
     */
    private String status;
    /**
     *  datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
     */
    private String createTime;
    /**
     *  varchar(36) DEFAULT NULL COMMENT '创建人',
     */
    private String creator;
    /**
     *  datetime DEFAULT NULL COMMENT '失效时间',
     */
    private String disableTime;
    /**
     *  timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '时间戳',
     */
    private String ts;
    /**
     *  char(1) DEFAULT '0' COMMENT '是否删除：0=否，1=是，默认0，参见yesorno',
     */
    private String deleteFlag;
}
