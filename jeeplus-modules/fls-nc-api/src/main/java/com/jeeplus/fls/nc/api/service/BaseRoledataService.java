package com.jeeplus.fls.nc.api.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jeeplus.fls.nc.api.domain.BaseRoledata;
import com.jeeplus.fls.nc.api.mapper.BaseRoledataMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * created 2023/12/21 16:33:04
 */
@Service
@DS("fls_db")
@Transactional(propagation = Propagation.NOT_SUPPORTED)
public class BaseRoledataService extends ServiceImpl<BaseRoledataMapper, BaseRoledata> {
    public List<BaseRoledata> queryByOrgAndRoles(String idOrg, List<String> roldIds) {
        return this.lambdaQuery().eq(BaseRoledata::getIdOrg, idOrg)
                .in(BaseRoledata::getIdRole, roldIds)
                .eq(BaseRoledata::getDeleteFlag,"0")
                .eq(BaseRoledata::getStatus, "2")
                .list();
    }
    
    public List<BaseRoledata> queryByDepartAndRoles(String idDepart, List<String> roldIds) {
        return this.lambdaQuery().eq(BaseRoledata::getIdDepartment, idDepart)
                .in(BaseRoledata::getIdRole, roldIds)
                .eq(BaseRoledata::getDeleteFlag,"0")
                .eq(BaseRoledata::getStatus, "2")
                .list();
    }
}
