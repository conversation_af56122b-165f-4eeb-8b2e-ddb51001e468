package com.jeeplus.fls.nc.api.domain;

import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

@Data
@TableName("t_base_res_trantype")
public class BaseResTrantype {
	private String idResTrantype;// '主键',
	private String idResource;// '资源主键',
	private String resourceCode;// '资源编码',
	private String tranType;// '交易类型',
	private String needprocFlag;// '是否启用审批流，0=假，1=真，参见trueorfalse',
	private String status;// '启用状态：1=未启用，2=已启用，3=已停用，默认1，参见basestatus',
	private String deleteFlag;// '是否删除：0=否，1=是，默认0，参见yesorno',
	private String tranCode;// '交易类型编码',
	private String presetFlag;// '是否系统预置=否，1=是，参见yesorno',
	private int displayOrder;// '显示顺序',
	private String incalLink;// '资源审核页url地址',
	private String pkBilltypeid;// 'NC交易类型主键',

}
