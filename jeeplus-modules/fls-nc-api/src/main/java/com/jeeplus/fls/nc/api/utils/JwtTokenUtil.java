package com.jeeplus.fls.nc.api.utils;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.auth0.jwt.algorithms.Algorithm;
import io.jsonwebtoken.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;


/**
 * JwtToken工具类
 */
@SuppressWarnings("ALL")
@Component
@Slf4j
public class JwtTokenUtil {


    private static final String TOKEN_SECRET = "fls@#$%$^&^*&(*()_!~$%$%^%*^&(&*(*))__)+*^$&$%@$$!$";

    /**
     * 生成token
     *
     */
    public  String generateToken(JwtPayLoad jwtPayLoad) {

        DateTime expirationDate = DateUtil.offsetSecond(new Date(), 30000);
        String jwtToken =  Jwts.builder()
                .setClaims(BeanUtil.beanToMap(jwtPayLoad))
                .setSubject(jwtPayLoad.getUserId())
                .setIssuedAt(new Date())
                .setExpiration(expirationDate)
                .signWith(SignatureAlgorithm.HS512, TOKEN_SECRET.getBytes())
                .compact();
        return jwtToken;
    }

    /**
     * 根据token获取Claims
     *
     */
    public static Claims getClaimsFromToken(String token) {

        return Jwts.parser()
                .setSigningKey(TOKEN_SECRET.getBytes())
                .parseClaimsJws(token)
                .getBody();
    }


//    public static void main(String[] args) {
//        System.out.println(Jwts.parser().setSigningKey("fls@#$%$^&^*&(*()_!~$%$%^%*^&(&*(*))__)+*^$&$%@$$!$".getBytes())
//                .parseClaimsJws("eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJmbHNtb2JpbGVhcGkiLCJhdWQiOiIiLCJpYXQiOjE2OTc1MTAyMzIsIm5iZiI6MTY5NzUxMDIzMiwiZXhwIjoxNjk3NTQ2MjMyLCJ1aWQiOiIxMiIsInBhc3N3b3JkIjoiMTIzNDU2IiwiaWRfdXNlciI6ImUxODQ0MWNiLWQyODUtNDJiNy1hZGEzLWYxYjM4Yzc2NGUyOSJ9.s4reT3y6465tcnJUUKpIgZjHjr3HsL8BSDMPfyyQ9sc").getBody());
//    }

    /**
     * 获取JwtPayLoad部分
     *
     */
    @SuppressWarnings("AliDeprecation")
    public static JwtPayLoad getJwtPayLoad(String token) {
        Claims claims = getClaimsFromToken(token);
        return BeanUtil.mapToBean(claims, JwtPayLoad.class, false);
    }

    public static JwtPayLoad getJwtPlayLoad(HttpServletRequest request){
        String token = request.getHeader("token");
        if (ObjectUtils.isEmpty(token)){
            return null;
        }
        return getJwtPayLoad(token);
    }

    /**
     * 校验token是否正确
     *
     */
    public static Boolean checkToken(String token) {
        try {
            getClaimsFromToken(token);
            return true;
        } catch (JwtException jwtException) {
            return false;
        }
    }

    /**
     * 校验token是否失效
     *
     */
    public static Boolean isTokenExpired(String token) {
        try {
            Claims claims = getClaimsFromToken(token);
            final Date expiration = claims.getExpiration();
            return expiration.before(new Date());
        } catch (ExpiredJwtException expiredJwtException) {
            return true;
        }
    }

    public static String sign(String userId, String source)
    {
        try {
            // 帐号加JWT私钥加密
            Algorithm algorithm = Algorithm.HMAC256(TOKEN_SECRET);
            // 附带account帐号信息
            return com.auth0.jwt.JWT.create()
                    .withClaim("user-id", userId)
                    .withClaim("source", source)
                    .withClaim("timestamp-key", new Date().getTime())
                    .sign(algorithm);
        } catch (Exception e) {
            log.error("JWTToken加密出现UnsupportedEncodingException异常:{}", e.getMessage());
            throw new RuntimeException("JWTToken加密出现UnsupportedEncodingException异常:" + e.getMessage());
        }
    }
}
