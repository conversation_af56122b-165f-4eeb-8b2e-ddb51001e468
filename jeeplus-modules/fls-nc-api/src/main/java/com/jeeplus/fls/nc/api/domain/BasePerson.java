package com.jeeplus.fls.nc.api.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * <AUTHOR>
 * created 2023/11/7 16:29:57
 */
@Data
@TableName("t_base_person")
public class BasePerson {
    /**
     *  varchar(36) NOT NULL COMMENT '主键',
     */
    private String idPerson;
    /**
     *  varchar(20) NOT NULL COMMENT '编码',
     */
    private String code;
    /**
     *  varchar(50) NOT NULL COMMENT '名称',
     */
    private String name;
    /**
     *  varchar(50) DEFAULT NULL COMMENT '曾用名',
     */
    private String usedname;
    /**
     *  char(1) DEFAULT NULL COMMENT '性别 1=男，2=女，3=不详 参见sex',
     */
    private String sex;
    /**
     *  varchar(20) DEFAULT NULL COMMENT '民族',
     */
    private String nation;
    /**
     *  varchar(30) DEFAULT NULL COMMENT '办公电话',
     */
    private String officephone;
    /**
     *  varchar(30) DEFAULT NULL COMMENT '手机',
     */
    private String mobile;
    /**
     *  varchar(36) NOT NULL COMMENT '所属组织',
     */
    private String idOrg;
    /**
     *  varchar(36) DEFAULT NULL COMMENT '所属部门',
     */
    private String idDepartment;
    /**
     *  varchar(36) DEFAULT NULL COMMENT '所属岗位',
     */
    private String idPost;
    /**
     *  char(20) DEFAULT NULL COMMENT 'NC人员基本信息pk值',
     */
    private String pkPsndoc;
    /**
     *  char(1) DEFAULT '1' COMMENT '启用状态：1=未启用，2=已启用，3=已停用，默认1，参见basestatus',
     */
    private String status;
    /**
     *  datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
     */
    private String createTime;
    /**
     *  varchar(36) DEFAULT NULL COMMENT '创建人',
     */
    private String creator;
    /**
     *  datetime DEFAULT NULL COMMENT '失效时间',
     */
    private String disableTime;
    /**
     *  timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '时间戳',
     */
    private String ts;
    /**
     *  char(1) DEFAULT '0' COMMENT '是否删除：0=否，1=是，默认0，参见yesorno',
     */
    private String deleteFlag;
    /**
     *  char(1) DEFAULT NULL COMMENT '是否在岗，参见yesorno',
     */
    private String postFlag;
    /**
     *  varchar(36) DEFAULT NULL COMMENT '所属职务',
     */
    private String idJob;
    /**
     *  varchar(50) DEFAULT NULL COMMENT '邮箱',
     */
    private String email;
    /**
     *  varchar(36) DEFAULT NULL COMMENT '钉钉识别码id',
     */
    private String dingdid;
    /**
     *  varchar(40) DEFAULT NULL COMMENT 'iHR员工id',
     */
    private String idIhr;
}
