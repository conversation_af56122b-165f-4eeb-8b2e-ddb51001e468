package com.jeeplus.fls.nc.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.sql.Date;

/**
 * <AUTHOR>
 * @date 2023/8/28
 */
@Data
@Accessors(chain = true)
@TableName("t_base_user")
public class BaseUser {

    private static final long serialVersionUID = 1L;


    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String idUser;

    /**
     * 用户名
     */
    private String code;

    /**
     * 昵称
     */
    private String name;

    /**
     * 密码
     */
    private String password;

    /**
     * 身份类型：0=员工，1=客户，2=供应商，3=审计，4=外部系统，5=开发者，6=合作伙伴，默认0，参见identity_type
     */
    private String identityType;

    /**
     * 身份id
     */
    private String idIdentity;

    /**
     * NC用户pk值
     */
    private String pkUser;

    /**
     * NC身份pk值
     */
    private String pkBaseDoc;

    /**
     * 启用状态：1=未启用，2=已启用，3=已停用，默认1，参见basestatus
     */
    private String status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 失效时间
     */
    private Date disableTime;

    /**
     * 是否删除：0=否，1=是，默认0，参见yesorno
     */
    private String deleteFlag;


}
