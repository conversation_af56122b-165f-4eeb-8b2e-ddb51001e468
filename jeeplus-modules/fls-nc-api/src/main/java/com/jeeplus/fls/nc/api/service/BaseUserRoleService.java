package com.jeeplus.fls.nc.api.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jeeplus.fls.nc.api.domain.BaseUserRole;
import com.jeeplus.fls.nc.api.mapper.BaseUserRoleMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * created 2023/12/22 11:17:40
 */
@Service
@DS("fls_db")
@Transactional(propagation = Propagation.NOT_SUPPORTED)
public class BaseUserRoleService extends ServiceImpl<BaseUserRoleMapper, BaseUserRole> {
    public List<BaseUserRole> queryWithRoles(List<String> roles){
        return this.lambdaQuery().in(BaseUserRole::getIdRole, roles)
                .eq(BaseUserRole::getDeleteFlag, "0")
                .eq(BaseUserRole::getStatus, "2")
                .list();
    }
}
