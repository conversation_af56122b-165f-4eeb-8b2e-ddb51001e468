package com.jeeplus.fls.nc.api.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * <AUTHOR>
 * created 2023/11/29 15:07:07
 */
@Data
@TableName("t_base_userrole")
public class BaseUserRole {
    /**
     *  varchar(36) NOT NULL COMMENT '主键',
     */
    private String idUserrole;
    /**
     *  varchar(36) NOT NULL COMMENT '用户id',
     */
    private String idUser;
    /**
     *  varchar(36) NOT NULL COMMENT '角色id',
     */
    private String idRole;
    /**
     *  char(1) DEFAULT '1' COMMENT '启用状态：1=未启用，2=已启用，3=已停用，默认1，参见basestatus',
     */
    private String status;
    /**
     *  datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
     */
    private String createTime;
    /**
     *  varchar(36) DEFAULT NULL COMMENT '创建人',
     */
    private String creator;
    /**
     *  datetime DEFAULT NULL COMMENT '失效时间',
     */
    private String disableTime;
    /**
     *  timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '时间戳',
     */
    private String ts;
    /**
     *  char(1) DEFAULT '0' COMMENT '是否删除：0=否，1=是，默认0，参见yesorno',
     */
    private String deleteFlag;
}
