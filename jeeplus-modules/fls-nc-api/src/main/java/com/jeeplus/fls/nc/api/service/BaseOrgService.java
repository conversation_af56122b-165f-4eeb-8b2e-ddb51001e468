package com.jeeplus.fls.nc.api.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jeeplus.fls.nc.api.domain.BaseOrg;
import com.jeeplus.fls.nc.api.mapper.BaseOrgMapper;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * created 2023/11/11 20:50:21
 */
@Service
@DS("fls_db")
public class BaseOrgService extends ServiceImpl<BaseOrgMapper, BaseOrg> {
    public List<BaseOrg> getOrgList(Collection<String> idList){
        return this.lambdaQuery()
                .in(!ObjectUtils.isEmpty(idList),BaseOrg::getIdOrg, idList)
                .eq(BaseOrg::getDeleteFlag, "0")
                .list();
    }
}
