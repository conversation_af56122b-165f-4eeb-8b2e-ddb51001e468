package com.jeeplus.fls.nc.api.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * <AUTHOR>
 * created 2023/11/7 14:33:50
 */
@Data
@TableName("t_base_org")
public class BaseOrg {
    /**
     *  varchar(36) NOT NULL COMMENT '组织主键',
     */
    private String idOrg;
    /**
     *  varchar(40) NOT NULL COMMENT '组织编码',
     */
    private String code;
    /**
     *  varchar(20) DEFAULT NULL COMMENT '内部编码',
     */
    private String innercode;
    /**
     *  varchar(200) NOT NULL COMMENT '组织名称',
     */
    private String name;
    /**
     *  varchar(200) DEFAULT NULL COMMENT '组织简称',
     */
    private String shortname;
    /**
     *  char(20) DEFAULT NULL COMMENT 'NC组织pk值',
     */
    private String pkOrg;
    /**
     *  char(1) DEFAULT NULL COMMENT '是否采购 0=否，1=是 参见yesorno',
     */
    private String purchaseFlag;
    /**
     *  char(1) DEFAULT NULL COMMENT '是否销售 0=否，1=是 参见yesorno',
     */
    private String salesFlag;
    /**
     *  char(1) DEFAULT NULL COMMENT '是否物流 0=否，1=是 参见yesorno',
     */
    private String trafficFlag;
    /**
     *  char(1) DEFAULT NULL COMMENT '是否财务 0=否，1=是 参见yesorno',
     */
    private String financeFlag;
    /**
     *  char(1) DEFAULT NULL COMMENT '是否库存 0=否，1=是 参见yesorno',
     */
    private String stockFlag;
    /**
     *  char(1) DEFAULT NULL COMMENT '是否人力资源 0=否，1=是 参见yesorno',
     */
    private String hrFlag;
    /**
     *  char(1) DEFAULT NULL COMMENT '是否行政 0=否，1=是 参见yesorno',
     */
    private String adminFlag;
    /**
     *  char(1) DEFAULT NULL COMMENT '是否独立法人 0=否，1=是 参见yesorno',
     */
    private String companyFlag;
    /**
     *  char(1) DEFAULT NULL COMMENT '组织类型 1=分公司，2=子公司，3=孙公司 参见org_type',
     */
    private String orgType;
    /**
     *  varchar(36) DEFAULT NULL COMMENT '父级组织',
     */
    private String idParentorg;
    /**
     *  varchar(36) DEFAULT NULL COMMENT '负责人',
     */
    private String orgManager;
    /**
     *  varchar(36) DEFAULT NULL COMMENT '分管领导',
     */
    private String orgLeader;
    /**
     *  varchar(50) DEFAULT NULL COMMENT '经纬度',
     */
    private String latLongAlt;
    /**
     *  varchar(36) DEFAULT NULL COMMENT '所属集团',
     */
    private String idGroup;
    /**
     *  char(1) DEFAULT '1' COMMENT '启用状态：1=未启用，2=已启用，3=已停用，默认1，参见basestatus',
     */
    private String status;
    /**
     *  datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
     */
    private String createTime;
    /**
     *  varchar(36) DEFAULT NULL COMMENT '创建人',
     */
    private String creator;
    /**
     *  datetime DEFAULT NULL COMMENT '失效时间',
     */
    private String disableTime;
    /**
     *  timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '时间戳',
     */
    private String ts;
    /**
     *  char(1) DEFAULT '0' COMMENT '是否删除：0=否，1=是，默认0，参见yesorno',
     */
    private String deleteFlag;
    /**
     *  varchar(36) DEFAULT NULL COMMENT '开票组织',
     */
    private String idOrgInvoice;
    /**
     *  char(1) DEFAULT '0' COMMENT '注销标识',
     */
    private String cancelFlag;
    /**
     *  varchar(40) DEFAULT NULL COMMENT 'iHR组织id',
     */
    private String idIhr;
}
