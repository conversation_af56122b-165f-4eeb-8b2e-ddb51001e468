package com.jeeplus.fls.nc.api.domain;

import java.util.List;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

@Data
@TableName("t_base_resourceclass")
public class BaseResourceClass {
	private String idResourceclass;
	
	private String code;
	
	private String name;
	
	private int grade;
	
	private String idParentclass;
	
	private String endFlag;
	
	private String status;
	
	private String deleteFlag;
	
	@TableField(exist = false)
	private List<BaseResourceClass> subclassList;
	
	@TableField(exist = false)
	private List<BaseResource> resourceList;
}
