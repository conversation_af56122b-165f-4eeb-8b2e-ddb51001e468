package com.jeeplus.fls.nc.api.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * <AUTHOR>
 * created 2023/11/10 09:41:15
 */
@Data
@TableName("t_base_post")
public class BasePost {
    /**
     *  varchar(36) NOT NULL COMMENT '主键',
     */
    private String idPost;
    /**
     *  varchar(20) DEFAULT NULL COMMENT '编码',
     */
    private String code;
    /**
     *  varchar(50) NOT NULL COMMENT '名称',
     */
    private String name;
    /**
     *  varchar(36) DEFAULT NULL COMMENT '所属组织',
     */
    private String idOrg;
    /**
     *  varchar(36) NOT NULL COMMENT '所属集团',
     */
    private String idGroup;
    /**
     *  varchar(36) NOT NULL COMMENT '所属部门',
     */
    private String idDepartment;
    /**
     *  varchar(36) DEFAULT NULL COMMENT '岗位序列',
     */
    private String idPostseries;
    /**
     *  varchar(36) DEFAULT NULL COMMENT '上级岗位',
     */
    private String idParentpost;
    /**
     *  varchar(36) DEFAULT NULL COMMENT '所属职务',
     */
    private String idJob;
    /**
     *  char(10) DEFAULT NULL COMMENT '成立日期 格式yyyy-mm-dd',
     */
    private String buildDate;
    /**
     *  char(10) DEFAULT NULL COMMENT '撤销日期 格式yyyy-mm-dd',
     */
    private String abortDate;
    /**
     *  char(20) DEFAULT NULL COMMENT 'NC岗位PK',
     */
    private String pkPost;
    /**
     *  char(1) DEFAULT '1' COMMENT '启用状态：1=未启用，2=已启用，3=已停用，默认1，参见basestatus',
     */
    private String status;
    /**
     *  datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
     */
    private String createTime;
    /**
     *  varchar(36) DEFAULT NULL COMMENT '创建人',
     */
    private String creator;
    /**
     *  datetime DEFAULT NULL COMMENT '失效时间',
     */
    private String disableTime;
    /**
     *  timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '时间戳',
     */
    private String ts;
    /**
     *  char(1) DEFAULT '0' COMMENT '是否删除：0=否，1=是，默认0，参见yesorno',
     */
    private String deleteFlag;
    /**
     *  varchar(40) DEFAULT NULL COMMENT 'iHR岗位id',
     */
    private String idIhr;
}
