package com.jeeplus.fls.nc.api.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * created 2023/11/13 21:53:05
 */
@Data
@TableName("t_base_roledata_bizunit")
public class BaseRoledataBizunit {
    /**
     *  varchar(36) NOT NULL COMMENT '主键',
     */
    private String idRoledataBizunit;
    /**
     *  varchar(36) DEFAULT NULL COMMENT '经营主体ID',
     */
    private String idBizunit;
    /**
     *  varchar(36) DEFAULT NULL COMMENT '角色ID',
     */
    private String idRole;
    /**
     *  char(1) DEFAULT NULL COMMENT '启用状态 1=未启用，2=已启用，3=已停用 参见basestatus',
     */
    private String status;
    /**
     *  datetime DEFAULT NULL COMMENT '创建时间',
     */
    private Date createTime;
    /**
     *  varchar(255) DEFAULT NULL COMMENT '创建人',
     */
    private String creator;
    /**
     *  datetime DEFAULT NULL COMMENT '失效时间',
     */
    private Date disableTime;
    /**
     *  char(19) DEFAULT NULL COMMENT '时间戳',
     */
    private Date ts;
    /**
     *  char(1) DEFAULT NULL COMMENT '是否删除 0=否，1=是 参见yesorno',
     */
    private String deleteFlag;
}
