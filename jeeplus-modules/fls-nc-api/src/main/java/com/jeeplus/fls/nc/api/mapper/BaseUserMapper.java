package com.jeeplus.fls.nc.api.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jeeplus.fls.nc.api.domain.BaseUser;
import com.jeeplus.fls.nc.api.domain.SysLoginUser;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/8/28
 */
public interface BaseUserMapper extends BaseMapper<BaseUser> {
//    Page<MaintenanceUserVo> getPage(Page<?> page, String keyword);
    SysLoginUser getUserInfoById(@Param("idUser") String idUser);
    SysLoginUser getUserInfoByCode(@Param("code") String code);
    List<SysLoginUser> selectByRoles(@Param("idRoles") List<String> idRoles);
}
