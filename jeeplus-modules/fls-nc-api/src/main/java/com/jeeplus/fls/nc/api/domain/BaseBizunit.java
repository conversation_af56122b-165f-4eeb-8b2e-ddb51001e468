package com.jeeplus.fls.nc.api.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * <AUTHOR>
 * created 2023/11/13 21:53:05
 */
@Data
@TableName("t_base_bizunit")
public class BaseBizunit {
    /**
     *  varchar(36) NOT NULL COMMENT '主键',
     */
    private String idBizunit;
    /**
     *  varchar(50) NOT NULL COMMENT '名称',
     */
    private String name;
    /**
     *  int(11) DEFAULT NULL COMMENT '显示顺序',
     */
    private String displayOrder;
    /**
     *  varchar(5) DEFAULT NULL COMMENT '内部编号',
     */
    private String innercode;
    /**
     *  char(1) DEFAULT NULL COMMENT '经营主体类型 1=终端，2=批发，3=外贸 参见bizunit_type',
     */
    private String bizunitType;
    /**
     *  date DEFAULT NULL COMMENT '开始日期',
     */
    private String beginDate;
    /**
     *  date DEFAULT NULL COMMENT '结束日期',
     */
    private String endDate;
    /**
     *  varchar(36) DEFAULT NULL COMMENT '所属集团',
     */
    private String idGroup;
    /**
     *  char(1) DEFAULT '1' COMMENT '启用状态：1=未启用，2=已启用，3=已停用，默认1，参见basestatus',
     */
    private String status;
    /**
     *  datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
     */
    private String createTime;
    /**
     *  varchar(36) DEFAULT NULL COMMENT '创建人',
     */
    private String creator;
    /**
     *  datetime DEFAULT NULL COMMENT '失效时间',
     */
    private String disableTime;
    /**
     *  timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '时间戳',
     */
    private String ts;
    /**
     *  char(1) DEFAULT '0' COMMENT '是否删除：0=否，1=是，默认0，参见yesorno',
     */
    private String deleteFlag;
    /**
     *  varchar(36) DEFAULT NULL COMMENT '核算归属主体',
     */
    private String idAccunit;
    /**
     *  varchar(36) DEFAULT NULL COMMENT '所属组织',
     */
    private String idOrg;
    /**
     *  varchar(36) DEFAULT NULL COMMENT '所属地区名称',
     */
    private String areaName;
    /**
     *  varchar(36) DEFAULT NULL COMMENT '经理',
     */
    private String idManager;
    /**
     *  varchar(36) DEFAULT NULL COMMENT '维修经理',
     */
    private String idManagerMaintenance;
    /**
     *  varchar(400) DEFAULT NULL COMMENT '服务支持人员',
     */
    private String idsServiceSpporter;
    /**
     *  varchar(400) DEFAULT NULL COMMENT '业务支持人员',
     */
    private String idsBusinessSpporter;
    /**
     *  varchar(36) DEFAULT NULL COMMENT '商业维修库',
     */
    private String idWarehouseCmaint;
    /**
     *  varchar(36) DEFAULT NULL COMMENT '自行维修库',
     */
    private String idWarehouseSrepair;
    /**
     *  varchar(36) DEFAULT NULL COMMENT '自检登记机关 checking_city_dep',
     */
    private String checkingCityDep;
    /**
     *  varchar(100) DEFAULT NULL COMMENT '社会信用代码',
     */
    private String appunitcode;
    /**
     *  varchar(36) DEFAULT NULL COMMENT '资料关联id',
     */
    private String idLinkDoc;
    /**
     *  varchar(36) DEFAULT NULL COMMENT '单据编码，没用的值，仅用于资料上传不报错',
     */
    private String billCode;
    /**
     *  varchar(100) DEFAULT NULL COMMENT '上牌单位邮政编码',
     */
    private String usecompostcode;
    /**
     *  varchar(100) DEFAULT NULL COMMENT '上牌单位地址',
     */
    private String usecomplace;
    /**
     *  varchar(100) DEFAULT NULL COMMENT '上牌单位固定电话',
     */
    private String usecomtel;
    /**
     *  varchar(100) DEFAULT '' COMMENT '上牌单位地址行政编码',
     */
    private String usecomregion;
    /**
     *  varchar(36) DEFAULT '' COMMENT '安全管理员',
     */
    private String checkSecurity;
    /**
     *  varchar(36) DEFAULT '' COMMENT '安全管理员电话',
     */
    private String checkSecurityTel;
    /**
     *  varchar(36) DEFAULT '' COMMENT '自检人员',
     */
    private String checkSelf;
}
