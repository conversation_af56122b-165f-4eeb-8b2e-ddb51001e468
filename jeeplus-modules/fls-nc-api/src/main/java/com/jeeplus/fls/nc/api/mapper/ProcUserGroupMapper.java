package com.jeeplus.fls.nc.api.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jeeplus.fls.nc.api.domain.UserGroup;

public interface ProcUserGroupMapper extends BaseMapper<UserGroup>{
	UserGroup getProcessUserGroupByCode(@Param("code") String code);
	
	List<String> getUserIdsByGroupId(@Param("userGroupId") String userGroupId);
}
