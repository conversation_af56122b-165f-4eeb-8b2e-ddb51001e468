package com.jeeplus.fls.nc.api.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jeeplus.fls.nc.api.domain.BaseRoledataBizunit;
import com.jeeplus.fls.nc.api.mapper.BaseRoledataBizunitMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * created 2023/12/21 16:33:04
 */
@Service
@DS("fls_db")
@Transactional(propagation = Propagation.NOT_SUPPORTED)
public class BaseRoledataBizunitService extends ServiceImpl<BaseRoledataBizunitMapper, BaseRoledataBizunit> {
    public List<BaseRoledataBizunit> queryByBizunitAndRoles(String bizunitId, List<String> roldIds) {
        return this.lambdaQuery().eq(BaseRoledataBizunit::getIdBizunit, bizunitId)
                .in(BaseRoledataBizunit::getIdRole, roldIds)
                .eq(BaseRoledataBizunit::getDeleteFlag,"0")
                .eq(BaseRoledataBizunit::getStatus, "2")
                .list();
    }
}
