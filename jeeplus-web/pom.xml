<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.jeeplus</groupId>
        <artifactId>jeeplus</artifactId>
        <version>9.0</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>jeeplus-web</artifactId>
    <packaging>jar</packaging>

    <name>jeeplus-web</name>
    <description>starter</description>


    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
    </properties>

    <dependencies>


        <dependency>
            <groupId>org.jeeplus</groupId>
            <artifactId>jeeplus-admin</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <dependency>
            <groupId>org.jeeplus</groupId>
            <artifactId>jeeplus-datasource</artifactId>
            <version>${project.parent.version}</version>
        </dependency>

        <dependency>
            <groupId>org.jeeplus</groupId>
            <artifactId>jeeplus-form</artifactId>
            <version>${project.parent.version}</version>
        </dependency>

        <dependency>
            <groupId>org.jeeplus</groupId>
            <artifactId>jeeplus-flowable</artifactId>
            <version>${project.parent.version}</version>
        </dependency>

        <dependency>
            <groupId>org.jeeplus</groupId>
            <artifactId>jeeplus-devtools-boot</artifactId>
            <version>9.25</version>
        </dependency>

        <dependency>
            <groupId>org.jeeplus</groupId>
            <artifactId>jeeplus-monitor</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <dependency>
            <groupId>org.jeeplus</groupId>
            <artifactId>jeeplus-tools</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <dependency>
            <groupId>org.jeeplus</groupId>
            <artifactId>jeeplus-notify</artifactId>
            <version>${project.parent.version}</version>
        </dependency>


        <dependency>
            <groupId>org.jeeplus</groupId>
            <artifactId>jeeplus-mail</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>org.jeeplus</groupId>-->
<!--            <artifactId>jeeplus-quartz</artifactId>-->
<!--            <version>${project.parent.version}</version>-->
<!--        </dependency>-->

        <!--<dependency>-->
        <!--<groupId>org.jeeplus</groupId>-->
        <!--<artifactId>jeeplus-layim</artifactId>-->
        <!--<version>${project.parent.version}</version>-->
        <!--</dependency>-->
        <dependency>
            <groupId>org.jeeplus</groupId>
            <artifactId>jeeplus-calendar</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <dependency>
            <groupId>org.jeeplus</groupId>
            <artifactId>jeeplus-ureport</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <dependency>
            <groupId>org.jeeplus</groupId>
            <artifactId>jeeplus-datascreen</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <dependency>
            <groupId>org.jeeplus</groupId>
            <artifactId>jeeplus-echarts</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <dependency>
            <groupId>org.jeeplus</groupId>
            <artifactId>jeeplus-file</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>org.jeeplus</groupId>-->
<!--            <artifactId>jeeplus-wps</artifactId>-->
<!--            <version>${project.parent.version}</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>org.jeeplus</groupId>-->
<!--            <artifactId>jeeplus-test</artifactId>-->
<!--            <version>${project.parent.version}</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>org.jeeplus</groupId>-->
<!--            <artifactId>jeeplus-fls</artifactId>-->
<!--            <version>${project.parent.version}</version>-->
<!--        </dependency>-->

<!--        <dependency>-->
<!--            <groupId>org.jeeplus</groupId>-->
<!--            <artifactId>fls-dev</artifactId>-->
<!--            <version>${project.parent.version}</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>org.jeeplus</groupId>
            <artifactId>fls-nc-api</artifactId>
            <version>9.0</version>
        </dependency>
        <dependency>
            <groupId>org.jeeplus</groupId>
            <artifactId>fls-flow</artifactId>
            <version>9.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.skywalking</groupId>
            <artifactId>apm-toolkit-logback-1.x</artifactId>
            <version>8.4.0</version>
        </dependency>
        <!--注册中心的依赖-->
<!--        <dependency>-->
<!--            <groupId>com.alibaba.cloud</groupId>-->
<!--            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>-->
<!--            <version>2021.0.1.0</version>-->
<!--        </dependency>-->
<!--        &lt;!&ndash; 配置中心的依赖 &ndash;&gt;-->
<!--        <dependency>-->
<!--            <groupId>com.alibaba.cloud</groupId>-->
<!--            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>-->
<!--            <version>2021.0.1.0</version>-->
<!--        </dependency>-->
    </dependencies>

    <build>
        <finalName>fls-flowable</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
        <!--</build>-->

        <!--<build>-->
        <!--<finalName>jeeplus-vue</finalName>-->
        <!--<plugins>-->
        <!--<plugin>-->
        <!--<groupId>org.springframework.boot</groupId>-->
        <!--<artifactId>spring-boot-maven-plugin</artifactId>-->
        <!--<configuration>-->
        <!--&lt;!&ndash; 指定该Main Class为全局的唯一入口 &ndash;&gt;-->
        <!--<mainClass>com.jeeplus.JeeplusWebApplication</mainClass>-->
        <!--</configuration>-->
        <!--</plugin>-->
        <!--<plugin>-->
        <!--<artifactId>maven-compiler-plugin</artifactId>-->
        <!--<configuration>-->
        <!--<source>1.8</source>-->
        <!--<target>1.8</target>-->
        <!--<encoding>UTF-8</encoding>-->
        <!--<compilerArguments>-->
        <!--<extdirs>lib</extdirs>-->
        <!--</compilerArguments>-->
        <!--</configuration>-->
        <!--</plugin>-->

        <!--</plugins>-->
        <resources>
            <resource>
                <directory>src/main/webapp</directory>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
            </resource>
        </resources>
    </build>

</project>
