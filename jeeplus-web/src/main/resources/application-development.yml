server:
  port: 8088
  servlet:
    context-path:
  tomcat:
    uri-encoding: UTF-8
#    basedir: /Users/<USER>
    accesslog:
      pattern: common
      enabled: true
      directory: logs
      prefix: jeeplus_access_log
      suffix: .log
      request-attributes-enabled: true
      rename-on-rotate: true
    max-http-form-post-size: 200MB
  jetty:
    max-http-form-post-size: 20000000B
logging:
  level:
    root: INFO
    #    org.flowable: DEBUG
    com.jeeplus: DEBUG
    org.apache.shiro.cache.ehcache.EhCacheManager: WARN
spring:
  servlet:
    multipart:
      maxFileSize:  1000MB
      maxRequestSize: 1000MB
  devtools:
    restart:
      enabled: true
  #  profiles:
  #    active: dev
  autoconfigure:
    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure
  #  jpa:
  #    open-in-view: false
  datasource:
    dynamic:
      druid: # 全局druid参数，绝大部分值和默认保持一致。(现已支持的参数如下,不清楚含义不要乱设置)
        SelectMethod: cursor
        filters: stat # 注意这个值和druid原生不一致，默认启动了stat,wall
        validation-query:  SELECT 'x'
        initial-size: 5
        min-idle: 5
        maxActive: 30
        maxWait: 60000
        timeBetweenEvictionRunsMillis: 60000
        minEvictableIdleTimeMillis: 300000
        testWhileIdle: true
        testOnBorrow: false
        testOnReturn: false
        poolPreparedStatements: true
        maxPoolPreparedStatementPerConnectionSize: 20
        connection-properties: druid.stat.mergeSql\=true;druid.stat.slowSqlMillis\=5000
        keep-alive: true
      p6spy: false # 默认false,线上必须关闭。开发阶段可以打开调试输出mybatis语句，但是有许多莫名其妙的bug，以及严重的性能问题，所以正式环境必须关闭。
      primary: master
      datasource:
        master:
          url: **********************************************************************************************************************************************************************************
          username: rt
          password: FLSrt@2023
          driver-class-name: com.mysql.cj.jdbc.Driver
  #  redis的配置
  redis:
    host: ************
    port: 6379
    database: 8
    password: FLSwx@123
    timeout: 3600000
#    host: 127.0.0.1
#    port: 6379
#    timeout: 3600000 #单位秒
#    database: 0
#    password: 123456
  rabbitmq:
    host: ************
    port: 5672
    username: test
    password: test
    virtual-host: /devops_message
    #确认消息已发送到交换机（Exchange）
    publisher-confirm-type: correlated
    #确认消息已发送到队列（Queue）
    publisher-returns: true
    #消费方消息确认，手动确认
    listener:
      simple:
        acknowledge-mode: manual
        default-requeue-rejected: false
        retry:
          #监听重试是否可用
          enabled: true
          #最大重试次数，默认为3
          max-attempts: 5
          max-interval: 60000

  mvc.servlet.load-on-startup: 1
  jmx:
    enabled: false
  # quartz任务配置
  quartz:
    job-store-type: jdbc
    properties:
      org:
        quartz:
          scheduler:
            instanceName: clusteredScheduler
            instanceId: AUTO
          jobStore:
            selectWithLockSQL: SELECT * FROM {0}LOCKS UPDLOCK WHERE LOCK_NAME = ?
            class: org.springframework.scheduling.quartz.LocalDataSourceJobStore
            tablePrefix: QRTZ_
            isClustered: false # 打开集群配置
            clusterCheckinInterval: 2000 # 设置集群检查间隔20s
            useProperties: false
          threadPool:
            class: org.quartz.simpl.SimpleThreadPool
            threadCount: 10
            threadPriority: 5
            threadsInheritContextClassLoaderOfInitializingThread: true
  # jackson配置
#  jackson:
#    default-property-inclusion: NON_NULL
#    time-zone: GMT+8
#    date-format: yyyy-MM-dd HH:mm:ss
flowable:
  # 关闭定时任务Job
  async-executor-activate: true
  check-process-definitions: false
  process-definition-location-prefix: classpath:/processes/
  database-schema-update: false
  common:
    app:
      idm-url: http://localhost:9999
      idm-admin:
        user: admin
        password: test
#mybatis-plus配置
mybatis-plus:
  config-location: classpath:/mybatis/mybatis-config.xml
  mapper-locations:
    - classpath*:mapper/*.xml
    - classpath*:com/jeeplus/**/*Mapper.xml
    - classpath:/META-INF/admin-mybatis-mappings/*.xml
    - classpath:/META-INF/modeler-mybatis-mappings/*.xml
  #实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: com.jeeplus.**.domain
  configuration-properties:
    prefix:
    boolValue: TRUE
    blobType: BLOB
  type-handlers-package: com.jeeplus.core.mapper
  global-config:
    db-config:
      logic-removeWithChildrenById-field: deleteFlag  # 全局逻辑删除的实体字段名(since 3.3.0,配置后可以忽略不配置步骤2)
      logic-removeWithChildrenById-value: 1 # 逻辑已删除值(默认为 1)
      logic-not-removeWithChildrenById-value: 0 # 逻辑未删除值(默认为 0)

#是否开启 swagger，生产环境请关闭
swagger:
  enable: false

#cas服务端的地址
cas:
  server-url-prefix: https://www.cainiao.com:8443/cas
#wps配置
wps:
  appid: 3f86c4255efd4e9c9d3ce6554e0fdf0d
  appsecret: 4bbc464724dc4351a93a70c95856197f
  download_host: http://demo1.jeeplus.org/jeeplus-vue
  domain: https://wwo.wps.cn
  downloadCallbackPath: /usr/local/wps
  webctx: /jeeplus-vue

#演示模式
demoMode: false

#oss配置
config:
  accessory:
    #local, aliyun, minIO
    type: local
    baseDir: local
    local:
      location: /Users/<USER>/Documents/xxx
    aliyun:
      endpoint:
      accessKeyId:
      accessKeySecret:
      bucketName:
    minIO:
      endpoint:
      accessKey:
      secretKey:
      bucketName:
#上传文件类型配置
file:
  allowedType: file  # 允许上传的文件类型， all, file ,image, audio, video, office
  extensions:
    all: all       # 允许上传所有类型文件
    file: 7z,aiff,asf,avi,bmp,csv,doc,docx,fla,flv,gif,gz,gzip,jpeg,jpg,mid,mov,mp3,mp4,mpc,mpeg,mpg,ods,odt,pdf,png,ppt,pptx,pxd,qt,ram,rar,rm,rmi,rmvb,rtf,sdc,sitd,swf,sxc,sxw,tar,tgz,tif,tiff,txt,vsd,wav,wma,wmv,xls,xlsx,zip       # 只允许上传安全文件（linux系统非可执行）
    image: gif,jpg,jpeg,bmp,png     # 只允许上传图片
    audio: CD,OGG,MP3,ASF,WMA,WAV,MP3PRO,RM,REAL,APE,MODULE,MIDI,VQF    # 只允许上传音频
    video: AVI,WMV,RM,RMVB,MPEG1,MPEG2,MPEG4(MP4),3GP,ASF,SWF,VOB,DAT,MOV,M4V,FLV,F4V,MKV,MTS,TS     # 只允许上传视频
    office: txt,xls,xlsx,xlsm,xltx,xltm,xlsb,xlam,doc,docx,docm,dotx,dotm,ppt,pptx,pptm,ppsx,ppsm,potx,potm,ppam     # 只允许上传office文件

#token过期时间
jwt.accessToken.expireTime: ********** # 30天，单位毫秒



##===============================#
##====== license 信息 ================#
##===============================#
## 开发者全局信息配置，优先级高于数据库中的配置，如果不启用将采用数据库中配置
#devtool:
#  enabled: false      # 是否启用本地配置, true/false
#  projectPath: /xx          #后端生成路径
#  frontPath: /xx    # 前端生成路径
#  packageName: com.jeeplus    # 默认包名
#  author: xx     # 默认作者
## 产品授权
#productId: Y20230908115
##license: D65FA50EF7DBA49424B59FCCE6BB793F6E3CCAF6BF9B4F62668968BEFD66E203990E526372740A9A509D9AB05B64B6C799C1CB63482CEC4A5716B175A7E6EF24
#license: 83EF94DAF858F66136E5EF05B7936C44E7C3ADDDE42DDD26048C7E96AFDA88FCFE2D7D27F524D4F60D696AC8885145AF5BC2B9EE150DF689D5EBF930EB201403

# 开发者签名认证
open-api:
  client:
    code: fls
    private-key: MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAKXEt3lLpcBeerw4dkhlDvyAs624WOdTjF2MO100lmWQbd1nTRG+wAfIKgGnuj7vSMaSeC6vlvyjQorBMfC5RA/NNQ9eO+gGh62sMzec7xgB4rva9rEoWvxpy5j3iyVeZN2LwO1+YHoiU3COucb4Z7iMjw6PTb9VB2RwEOwhDkcNAgMBAAECgYB81TQ50r7irAOPhJGGmRv9CMrGkuHYanF07SqzWOu9VlN1/Nnr+BLSiWkgz/eQd0/9wcVhmveAD04IAc8i8dGPP6V6xAw035UhGGojx4HEhEQsC5il1zMRv0rZ15u1KpZ0PbvEXtY02myxkZLc/Wq5UlHu/dFYgmrtmcAwGS0Q/QJBAOvS0VL+sqnsNpM41vQL2L/48LELZDsLnic930efkf1At8H9/m0TgQi178uOS+Ne8+dXnAkHdAGSDcYGnZpYDjMCQQCz84AYDn5fH36kWtIamM/RMt+ThnyM/affrHFZs8TFCryeRxyONXDQx7CbZIvsXQ9q4YK9oTpcM5of2JgaiJW/AkA57QTh4BI+zvjw3MtgEvDg5uMoeeXa4xWsCfSogTe4/4ajriIq5NmTLC2E0KUvaol6z/hR93S1VgzwEXvxB6QlAkAlP9xeHnHuQti2MM6ZDtTF2yAIq6gQj1k2pb148gtjNkvA/7tbfJhzv0JBiFimfsT4POIRWtR3zX6z5OPnaiqFAkEAsc+oWdJR1P9XDIeLgbzeEzEtC188Bnt2rmgtJwtUDZWdvjAPilPxJJOvVVAA09S7jJhwzdhMbmkGeDu/tjGxRA==
    gateway-host: http://************:8090
