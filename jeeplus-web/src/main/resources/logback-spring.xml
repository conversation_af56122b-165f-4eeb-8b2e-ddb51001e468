<?xml version="1.0" encoding="UTF-8"?>

<configuration scan="false" scanPeriod="60 seconds" debug="false">
    <springProperty scope="context" name="log.robot.weixin.errorToken" source="log.robot.weixin.errorToken"/>
    <springProperty scope="context" name="log.filePath" source="log.filePath"/>
    <springProperty scope="context" name="spring.application.name" source="spring.application.name"/>
    <springProperty scope="context" name="spring.application.name" source="spring.application.name"/>

    <conversionRule conversionWord="clr" converterClass="org.springframework.boot.logging.logback.ColorConverter"/>
    <conversionRule conversionWord="wex"
                    converterClass="org.springframework.boot.logging.logback.WhitespaceThrowableProxyConverter"/>
    <conversionRule conversionWord="wEx"
                    converterClass="org.springframework.boot.logging.logback.ExtendedWhitespaceThrowableProxyConverter"/>
    <property name="LOG_PATH" value="./logs"/>
    <property name="CONSOLE_LOG_PATTERN"
              value="%clr(%d{${LOG_DATEFORMAT_PATTERN:-yyyy-MM-dd HH:mm:ss.SSS}}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} [%tid] [DD:%X{dd.trace_id}] %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}"/>
    <property name="CONSOLE_LOG_PATTERN_NO_CLR"
              value="[%d{yyyy-MM-dd HH:mm:ss SSS}][%thread][%level][%logger{15}:%line][%tid][DD:%X{dd.trace_id}] %m%n"/>
    <property name="FILE_LOG_PATTERN"
              value="[%d{yyyy-MM-dd HH:mm:ss SSS}][%thread][%level][%tid] %m%n"/>
    <property name="DING_LOG_PATTERN"
              value="[%d{yyyy-MM-dd HH:mm:ss.SSS}][%20.20(%thread)][%-40.40(%logger{40})]:[%tid] [DD:%X{dd.trace_id}] %msg%n"/>
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.TraceIdPatternLogbackLayout">
            <pattern>${CONSOLE_LOG_PATTERN_NO_CLR}</pattern>
        </layout>
    </appender>
    <!--ding alarm appender settings !end-->
    <appender name="file" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/${spring.application.name}.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>
                ${LOG_PATH}/${spring.application.name}-%d{yyyy-MM-dd}-%i.log
            </fileNamePattern>
            <MaxHistory>10</MaxHistory>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>10MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
        <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.TraceIdPatternLogbackLayout">
            <pattern>${FILE_LOG_PATTERN}</pattern>
        </layout>
    </appender>
<!--    <appender name="weixin_alarm_out" class="com.isoftstone.stone.framework.log.WeixinAlarmAppender">-->
<!--        <filter class="ch.qos.logback.classic.filter.LevelFilter">-->
<!--            <level>ERROR</level>-->
<!--            <onMatch>ACCEPT</onMatch>-->
<!--            <onMismatch>DENY</onMismatch>-->
<!--        </filter>-->
<!--        <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.TraceIdPatternLogbackLayout">-->
<!--            <pattern>${DING_LOG_PATTERN}</pattern>-->
<!--        </layout>-->
<!--        <token>${log.robot.weixin.errorToken}</token>-->
<!--        <title>${spring.application.name}-${spring.profiles.active}</title>-->
<!--    </appender>-->
    <root level="info">
        <appender-ref ref="console"/>
        <appender-ref ref="file"/>
<!--        <appender-ref ref="weixin_alarm_out" />-->
    </root>
</configuration>
