spring:
  application:
    name: fls-flowable
  profiles:
    active: development
#  cloud:
#    nacos:
#      # nacos 服务地址
#      server-addr: 192.168.1.51:8848
#      username: nacos
#      password: nacos
#      discovery:
#        # 注册组
#        group: DEFAULT_GROUP
#        namespace: test
#        service: fls-flowable
#      config:
#        # 配置组
#        group: DEFAULT_GROUP
#        namespace: test
#        file-extension: yml
#  config:
#    import:
#      - optional:nacos:fls-flowable.yml
##############################################
#
# thymeleaf静态资源配置


#
##############################################
# 默认路径
spring.thymeleaf.prefix: classpath:/templates/
# 后缀
spring.thymeleaf.suffix: .html
# 模板格式
spring.thymeleaf.mode: HTML5
spring.thymeleaf.encoding: UTF-8
