package com.jeeplus.config;


import com.baomidou.mybatisplus.core.injector.AbstractMethod;
import com.baomidou.mybatisplus.core.injector.DefaultSqlInjector;
import com.baomidou.mybatisplus.extension.injector.methods.LogicDeleteByIdWithFill;

import java.util.List;

/**
 * 自定义Sql注入
 */
public class MySqlInjector extends DefaultSqlInjector {

    @Override
    public List <AbstractMethod> getMethodList(Class <?> mapperClass) {
        List <AbstractMethod> methodList = super.getMethodList ( mapperClass );
        methodList.add ( new LogicDeleteByIdWithFill ( ) );
        return methodList;
    }
}

