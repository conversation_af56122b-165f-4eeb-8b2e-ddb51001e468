//package com.jeeplus.config;
//
//import com.jeeplus.modules.fls.biz.pojo.vo.BaseResult;
//import com.jeeplus.modules.fls.utils.ResultUtil;
//import lombok.extern.slf4j.Slf4j;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.web.bind.annotation.ControllerAdvice;
//import org.springframework.web.bind.annotation.ExceptionHandler;
//import org.springframework.web.bind.annotation.ResponseBody;
//
//@Slf4j
//@ControllerAdvice
//public class FlsExceptionTranslator {
//
//    private static final Logger LOGGER = LoggerFactory.getLogger(FlsExceptionTranslator.class);
//    @ResponseBody
//    @ExceptionHandler(RuntimeException.class)
//    public BaseResult<String> systemException(RuntimeException ex) {
//        LOGGER.info("系统异常:", ex);
//        return ResultUtil.error(ex.getMessage());
//    }
//}
