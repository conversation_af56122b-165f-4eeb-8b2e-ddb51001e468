package com.jeeplus.config;

import com.baomidou.mybatisplus.extension.plugins.handler.TenantLineHandler;
import com.jeeplus.sys.constant.CommonConstants;
import com.jeeplus.sys.utils.TenantUtils;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.StringValue;

/**
 * 租户拦截器
 */
public class TenantLineHandlerImpl implements TenantLineHandler {

    @Override
    public Expression getTenantId() {
        String tenantId = TenantUtils.getTenantId ();
        return new StringValue ( tenantId );
    }

    // 排除不需要拼多租户条件的表
    @Override
    public boolean ignoreTable(String tableName) {
        tableName = tableName.toLowerCase ( );
        if (tableName.startsWith("t_") || tableName.contains("columns")){
            return true;
        }
        // 多租户对于以下表不过滤, 所有租户可见
        switch (tableName) {
            case "sys_menu":
            case "sys_user_role":
            case "sys_user_post":
            case "sys_role_menu":
            case "sys_role_datarule":
            case "sys_area":
            case "sys_dict_type":
            case "sys_dict_value":
            case "sys_config":
            case "sys_tenant":
            case "sys_language":
            case "plugin_datasource_link":
                // fls甲方的数据库表
            case "t_lease_assets":
            case "t_base_asset_modelnumber":
            case "t_base_department":
            case "t_base_user":
            case "t_base_person":
            case "t_base_bizunit":
            case "t_base_brand":
            case "t_cust_baseinfo":
            case "t_api_nclog":
            case "t_base_org":
            case "t_supplier_baseinfo":
            case "t_base_warehouse":
            case "t_base_whpos":
            case "t_base_workteam":
            case "t_base_workteam_member":
            case "t_base_inventory":
            case "t_base_dict":
            case "t_base_measdoc":
                // 我们的表
            case "t_bill_log":
            case "t_material_apply_bill":
            case "t_material_apply_bill_relation":
            case "t_material_get_bill":
            case "t_material_get_bill_relation":
            case "t_material_get_record":
            case "t_material_receipt":
            case "t_material_receipt_information":
            case "t_material_use_record":
            case "t_notice":
            case "t_notice_read":
            case "t_purchase_apply_bill":
            case "t_purchase_apply_bill_relation":
            case "t_purchase_material_receipt":
            case "t_purchase_material_receipt_relation":
            case "t_repair_asset_information":
            case "t_repair_assets_detail":
            case "t_repair_bill":
            case "t_repair_bill_assets_relation":
            case "t_repair_log_bill":
            case "t_repair_log_bill_relation":
            case "t_repair_log_material_replenish_relation":
            case "t_repair_log_photo_video":
            case "t_repair_statistics":
            case "t_repair_statistics_detail":
            case "t_repair_task_bill":
            case "t_report_statistics":
            case "t_report_statistics_detail":
            case "t_stock":
            case "t_transfer_business_warehouse":
            case "t_transfer_business_warehouse_relation":
            case "t_transfer_in_apply_bill":
            case "t_transfer_in_apply_bill_relation":
            case "t_transfer_return_warehouse":
            case "t_transfer_return_warehouse_relation":
            case "t_transfer_to_bill":
            case "t_transfer_to_bill_relation":
            case "t_incoming_material_receipt":
            case "t_incoming_material_receipt_relation":
                return true;
        }
        // flowable工作流系统表（排除扩展表）采用自身saas方案实现租户模式，在这里做忽略处理
        if ( tableName.startsWith ( "act_" ) && !tableName.startsWith ( "act_extension" ) ) {
            return true;
        }

        // 如果默认租户是平台租户， 角色，用户等系统表不过滤
        if ( TenantUtils.getTenantId ().equals ( CommonConstants.DEFAULT_TENANT_ID ) ) {
            switch (tableName) {
                case "sys_user":
                case "sys_role":
                case "sys_post":
                case "sys_office":
                    return true;
            }
        }
        return false;
    }
}
