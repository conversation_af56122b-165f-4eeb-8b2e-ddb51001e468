package com.jeeplus.config;

import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class RedissonConfig {

    @Value("${spring.redis.host}") // 获取 Redis 主机地址
    private String redisHost;

    @Value("${spring.redis.port}") // 获取 Redis 端口
    private int redisPort;

    @Value("${spring.redis.database}") // 获取 database
    private int database;
    
    @Value("${spring.redis.password}") // 获取 Redis 密码（如果有）
    private String redisPassword;

    @Bean
    public RedissonClient redissonClient() {
        Config config = new Config();
        // 配置单节点模式
        config.useSingleServer()
              .setAddress("redis://" + redisHost + ":" + redisPort) // 设置 Redis 地址
              .setPassword(redisPassword).setDatabase(database); // 设置 Redis 密码
        config.useSingleServer().setPingConnectionInterval(0);
        // 创建 Redisson 客户端
        return Redisson.create(config);
    }
}