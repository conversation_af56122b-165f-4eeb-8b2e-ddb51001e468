//package com.jeeplus.core.filter;
//
//import cn.hutool.json.JSONUtil;
//import com.jeeplus.common.redis.RedisUtils;
//import com.jeeplus.config.properties.JeePlusProperties;
//import com.jeeplus.fls.nc.api.domain.SysLoginUser;
//import com.jeeplus.fls.nc.api.service.SysLoginService;
//import com.jeeplus.fls.nc.api.utils.JwtPayLoad;
//import com.jeeplus.fls.nc.api.utils.JwtTokenUtil;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.core.annotation.Order;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//import javax.servlet.*;
//import javax.servlet.annotation.WebFilter;
//import javax.servlet.http.HttpServletRequest;
//import java.io.IOException;
//import java.util.Objects;
//
//@Slf4j
//@Order(1)
//@Component
//@WebFilter(urlPatterns = "/fls/*")
//public class UserFilter implements Filter {
//
//    public static final String PC_SOURCE = "pc";
//
//    public static final String APPLET_SOURCE = "applet";
//
//    @Resource
//    private SysLoginService sysLoginService;
//
//    @Override
//    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
//        // 通过ThreadLocal保存用户信息,方便接口取对应的用户信息
//        HttpServletRequest request = (HttpServletRequest) servletRequest;
//        String token = request.getHeader("token");
//        String clientSource = request.getHeader("client-source");
//        log.info(String.format("-----UserFilter, 来源为：%s----", clientSource));
//        log.info(String.format("------UserFilter, token信息为：%s--------", token));
//        // 小程序只有token，没有ticket
//        if (StringUtils.isNotBlank(token)) {
//            /**
//             * 只有登录后才能通过token获取到用户信息，不登录是获取不到的。
//             */
//            JwtPayLoad jwtPayLoad = JwtTokenUtil.getJwtPayLoad(token);
//            if (Objects.nonNull(jwtPayLoad)) {
//                // 获取用户信息
//                SysLoginUser sysLoginUser = sysLoginService.getUserInfoByApplet(jwtPayLoad.getId_user());
//                // 转换成JSON字符串
//                String userStr = JSONUtil.parseObj(sysLoginUser, false).toStringPretty();
//                // set到当前线程
////                MdcUtil.add(userStr);
//                // set 到redis中 2小时
//                RedisUtils.getInstance().set(APPLET_SOURCE + ":" + token, userStr, JeePlusProperties.newInstance().getEXPIRE_TIME());
//            }
//        }
//        filterChain.doFilter(servletRequest, servletResponse);
//    }
//}
