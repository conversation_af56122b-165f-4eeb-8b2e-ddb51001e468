INSERT INTO `fls_flowable`.`sys_menu`(`id`, `parent_id`, `parent_ids`, `name`, `sort`, `href`, `target`, `icon`, `is_show`, `permission`, `create_by_id`, `create_time`, `update_by_id`, `update_time`, `remarks`, `del_flag`, `menu_type`, `affix`, `tenant_id`, `code`, `hidden_breadcrumb`) VALUES ('a734592d272a4859b8f76db16441111a', 'ecd805be31dd4fa5b8c1b76a9a5ac85f', '0,1,62,ecd805be31dd4fa5b8c1b76a9a5ac85f', '单据资源', 30, '/flowable/bill/resource/list', NULL, 'liuchengsheji1', '1', 'flowable:model:list', '1', '2024-05-14 11:39:19', '1', '2024-05-14 11:39:16', NULL, 0, '1', '0', NULL, 't_flow_bill', '0');


CREATE TABLE `t_bill_metadata` (
  `id` char(36) NOT NULL,
  `bill_code` varchar(100) DEFAULT NULL COMMENT '单据编码',
  `bill_name` varchar(100) DEFAULT NULL COMMENT '单据名称',
  `metadata` text COMMENT '元数据JSON',
  `del_flag` char(1) DEFAULT NULL,
  `default_limit_type` char(1) DEFAULT NULL,
  `default_limit_val` varchar(1000) DEFAULT NULL,
  `form_id` varchar(100) DEFAULT NULL COMMENT '流程表单ID',
  `create_time` timestamp NULL DEFAULT NULL,
  `create_by_id` varchar(100) DEFAULT NULL,
  `update_time` timestamp NULL DEFAULT NULL,
  `update_by_id` varchar(100) DEFAULT NULL,
  `tenant_id` varchar(64) DEFAULT NULL,
  `formdata` text,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='单据资源初始化元数据信息表';

CREATE TABLE `t_bill_workflow` (
  `id` char(36) NOT NULL COMMENT '主键',
  `bill_code` varchar(100) DEFAULT NULL COMMENT '单据编码',
  `bill_name` varchar(100) DEFAULT NULL COMMENT '单据名称',
  `trans_type_code` varchar(100) DEFAULT NULL COMMENT '交易类型编码',
  `trans_type_name` varchar(100) DEFAULT NULL COMMENT '交易类型名称',
  `version` varchar(255) DEFAULT NULL COMMENT '版本',
  `flowable_model_id` varchar(50) DEFAULT NULL COMMENT '流程模型ID',
  `flowable_model_key` varchar(50) DEFAULT NULL COMMENT '流程模型KEY',
  `del_flag` char(1) DEFAULT NULL COMMENT '0-正常,1-删除',
  `create_time` datetime DEFAULT NULL,
  `create_by_id` varchar(100) DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  `update_by_id` varchar(100) DEFAULT NULL,
  `tenant_id` varchar(64) DEFAULT NULL,
  `status` char(1) DEFAULT '2' COMMENT '1=未启用，2=已启用，3=已停用',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE `t_bill_workflow_task` (
  `id` char(36) NOT NULL COMMENT '主键',
  `task_def_id` varchar(100) DEFAULT NULL COMMENT '审批任务节点ID唯一',
  `task_id` varchar(100) DEFAULT NULL COMMENT '流程设计的节点ID',
  `flowable_model_id` varchar(50) DEFAULT NULL COMMENT '流程模型ID',
  `flowable_model_key` varchar(50) DEFAULT NULL COMMENT '流程模型KEY',
  `limit_type` char(1) DEFAULT NULL COMMENT '限制类型\r\n为空 不限制\r\n1 同组织\r\n2 同部门\r\n3上级限定',
  `limit_val` varchar(2000) DEFAULT NULL COMMENT '限制值，与limit_type结合使用',
  `del_flag` char(1) DEFAULT NULL COMMENT '0-正常,1-删除',
  `create_time` datetime DEFAULT NULL,
  `create_by_id` varchar(100) DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  `update_by_id` varchar(100) DEFAULT NULL,
  `tenant_id` varchar(64) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;