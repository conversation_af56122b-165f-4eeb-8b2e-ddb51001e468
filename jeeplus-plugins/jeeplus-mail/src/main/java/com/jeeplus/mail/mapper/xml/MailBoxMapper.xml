<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jeeplus.mail.mapper.MailBoxMapper">

    <sql id="mailBoxColumns">
        a.id AS "id",
        a.read_status AS "readStatus",
        a.sender_id AS "sender.id",
        a.receiver_id AS "receiver.id",
        a.receiver_ids AS "receiverIds",
        a.send_time AS "sendTime",
        a.mail_id AS "mail.id",
        receiver.name AS "receiver.name",
        sender.name AS "sender.name",
        sender.photo AS "sender.photo",
        mail.title AS "mailDTO.title",
        mail.overview AS "mailDTO.overview",
        mail.content AS "mailDTO.content"
    </sql>

    <sql id="mailBoxJoins">
        LEFT JOIN plugin_mail mail ON mail.id = a.mail_id
        LEFT JOIN sys_user receiver ON receiver.id = a.receiver_id
        LEFT JOIN sys_user sender ON sender.id = a.sender_id
    </sql>

    <select id="get" resultType="com.jeeplus.mail.service.dto.MailBoxDTO">
        SELECT
        <include refid="mailBoxColumns"/>
        FROM plugin_mail_box a
        <include refid="mailBoxJoins"/>
        WHERE a.id = #{id}
    </select>

    <select id="findList" resultType="com.jeeplus.mail.service.dto.MailBoxDTO">
        SELECT
        <include refid="mailBoxColumns"/>
        FROM plugin_mail_box a
        <include refid="mailBoxJoins"/>
        ${ew.customSqlSegment}
        order by a.send_time desc
    </select>


</mapper>
