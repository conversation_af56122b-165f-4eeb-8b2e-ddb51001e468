package com.jeeplus.database.datamodel.service.mapstruct;

import com.jeeplus.core.mapstruct.EntityWrapper;
import com.jeeplus.database.datamodel.domain.DataSet;
import com.jeeplus.database.datamodel.service.dto.DataSetDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, uses = {})
public interface DataSetWrapper extends EntityWrapper <DataSetDTO, DataSet> {

    DataSetWrapper INSTANCE = Mappers.getMapper ( DataSetWrapper.class );

    @Mappings({
            @Mapping(source = "tenantDTO.id", target = "tenantId"),
            @Mapping(source = "dataSource.id", target = "dataSourceId"),
            @Mapping(source = "createBy.id", target = "createById"),
            @Mapping(source = "updateBy.id", target = "updateById")})
    DataSet toEntity(DataSetDTO dto);


    @Mappings({
            @Mapping(source = "tenantId", target = "tenantDTO.id"),
            @Mapping(source = "dataSourceId", target = "dataSource.id"),
            @Mapping(source = "createById", target = "createBy.id"),
            @Mapping(source = "updateById", target = "updateBy.id")})
    DataSetDTO toDTO(DataSet entity);

}
