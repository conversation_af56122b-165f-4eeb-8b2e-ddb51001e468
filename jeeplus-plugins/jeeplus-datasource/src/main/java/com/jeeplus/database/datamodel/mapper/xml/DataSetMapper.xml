<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jeeplus.database.datamodel.mapper.DataSetMapper">

    <sql id="dataSetColumns">
        a.id AS "id",
        a.data_source_id AS "dataSource.id",
        a.name AS "name",
        a.sql_cmd AS "sqlCmd",
        a.create_time AS "createTime",
        a.update_time AS "updateTime",
        dataSource.name AS "dataSource.name",
        dataSource.en_name AS "dataSource.enName"
    </sql>

    <sql id="dataSetJoins">
        LEFT JOIN plugin_datasource_link dataSource ON dataSource.id = a.data_source_id
    </sql>


    <select id="get" resultType="com.jeeplus.database.datamodel.service.dto.DataSetDTO">
        SELECT
        <include refid="dataSetColumns"/>
        FROM plugin_datasource_model a
        <include refid="dataSetJoins"/>
        WHERE a.id = #{id}
    </select>

    <select id="findList" resultType="com.jeeplus.database.datamodel.service.dto.DataSetDTO">
        SELECT
        <include refid="dataSetColumns"/>
        FROM plugin_datasource_model a
        <include refid="dataSetJoins"/>
        ${ew.customSqlSegment}
    </select>


</mapper>
