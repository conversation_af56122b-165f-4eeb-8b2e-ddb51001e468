package com.jeeplus.database.datatable.constants;

public interface FieldTypes {
    String ARRAY = "ARRAY";
    /**
     * The string representation of the {@link java.sql.Types#BIGINT} constant.
     */
    String BIGINT = "BIGINT";
    /**
     * The string representation of the {@link java.sql.Types#BINARY} constant.
     */
    String BINARY = "BINARY";
    /**
     * The string representation of the {@link java.sql.Types#BIT} constant.
     */
    String BIT = "BIT";
    /**
     * The string representation of the {@link java.sql.Types#BLOB} constant.
     */
    String BLOB = "BLOB";
    /**
     * The string representation of the {@link java.sql.Types#BOOLEAN} constant.
     */
    String BOOLEAN = "BOOLEAN";
    /**
     * The string representation of the {@link java.sql.Types#CHAR} constant.
     */
    String CHAR = "CHAR";
    /**
     * The string representation of the {@link java.sql.Types#CLOB} constant.
     */
    String CLOB = "CLOB";
    /**
     * The string representation of the {@link java.sql.Types#DATALINK} constant.
     */
    String DATALINK = "DAT<PERSON>INK";
    /**
     * The string representation of the {@link java.sql.Types#DATE} constant.
     */
    String DATE = "DATE";
    /**
     * The string representation of the {@link java.sql.Types#DECIMAL} constant.
     */
    String DECIMAL = "DECIMAL";
    /**
     * The string representation of the {@link java.sql.Types#DISTINCT} constant.
     */
    String DISTINCT = "DISTINCT";
    /**
     * The string representation of the {@link java.sql.Types#DOUBLE} constant.
     */
    String DOUBLE = "DOUBLE";
    /**
     * The string representation of the {@link java.sql.Types#FLOAT} constant.
     */
    String FLOAT = "FLOAT";
    /**
     * The string representation of the {@link java.sql.Types#INTEGER} constant.
     */
    String INTEGER = "INTEGER";
    /**
     * The string representation of the {@link java.sql.Types#JAVA_OBJECT} constant.
     */
    String JAVA_OBJECT = "JAVA_OBJECT";
    /**
     * The string representation of the {@link java.sql.Types#LONGVARBINARY} constant.
     */
    String LONGVARBINARY = "LONGVARBINARY";
    /**
     * The string representation of the {@link java.sql.Types#LONGVARCHAR} constant.
     */
    String LONGVARCHAR = "LONGVARCHAR";
    /**
     * The string representation of the {@link java.sql.Types#NULL} constant.
     */
    String NULL = "NULL";
    /**
     * The string representation of the {@link java.sql.Types#NUMERIC} constant.
     */
    String NUMERIC = "NUMERIC";
    /**
     * The string representation of the {@link java.sql.Types#OTHER} constant.
     */
    String OTHER = "OTHER";
    /**
     * The string representation of the {@link java.sql.Types#REAL} constant.
     */
    String REAL = "REAL";
    /**
     * The string representation of the {@link java.sql.Types#REF} constant.
     */
    String REF = "REF";
    /**
     * The string representation of the {@link java.sql.Types#SMALLINT} constant.
     */
    String SMALLINT = "SMALLINT";
    /**
     * The string representation of the {@link java.sql.Types#STRUCT} constant.
     */
    String STRUCT = "STRUCT";
    /**
     * The string representation of the {@link java.sql.Types#TIME} constant.
     */
    String TIME = "TIME";
    /**
     * The string representation of the {@link java.sql.Types#TIMESTAMP} constant.
     */
    String TIMESTAMP = "TIMESTAMP";
    /**
     * The string representation of the {@link java.sql.Types#TINYINT} constant.
     */
    String TINYINT = "TINYINT";
    /**
     * The string representation of the {@link java.sql.Types#VARBINARY} constant.
     */
    String VARBINARY = "VARBINARY";
    /**
     * The string representation of the {@link java.sql.Types#VARCHAR} constant.
     */
    String VARCHAR = "VARCHAR";
}
