<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jeeplus.echarts.mapper.EchartsMapper">

    <sql id="echartsColumns">
        a.id AS "id",
        a.name AS "name",
        a.data_set_id AS "dataSet.id",
        a.option_content AS "option",
        a.create_by_id AS "createBy.id",
        a.create_time AS "createTime",
        a.update_by_id AS "updateBy.id",
        a.update_time AS "updateTime",
        a.remarks AS "remarks",
        a.del_flag AS "delFlag",
        a.t_type AS "type",
        a.xnames AS "xnames",
        a.ynames AS "ynames",
        dataSet.name AS "dataSet.name"
    </sql>

    <sql id="echartsJoins">

        LEFT JOIN plugin_datasource_model dataSet ON dataSet.id = a.data_set_id
    </sql>


    <select id="findList" resultType="com.jeeplus.echarts.service.dto.EchartsDTO">
        SELECT
        <include refid="echartsColumns"/>
        FROM plugin_echarts a
        <include refid="echartsJoins"/>
        ${ew.customSqlSegment}
    </select>

</mapper>
