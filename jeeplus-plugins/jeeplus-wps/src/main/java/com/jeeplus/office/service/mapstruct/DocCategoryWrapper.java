package com.jeeplus.office.service.mapstruct;

import com.jeeplus.core.mapstruct.TreeWrapper;

import com.jeeplus.office.domain.DocCategory;
import com.jeeplus.office.service.dto.DocCategoryDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, uses = {})
public interface DocCategoryWrapper extends TreeWrapper <DocCategoryDTO, DocCategory> {

    DocCategoryWrapper INSTANCE = Mappers.getMapper ( DocCategoryWrapper.class );
    
    /**
     * dto对象转化成entity对象
     */
    @Mappings({})
    DocCategory toEntity(DocCategoryDTO dto);

    /**
     * entity对象转换成dto对象
     */
    @Mappings({})
    DocCategoryDTO toDTO(DocCategory entity);
}
