<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jeeplus.flowable.mapper.ModelExtMapper">

	<resultMap id="modelResultMap" type="com.jeeplus.flowable.model.ModelExt">
        <id property="id" column="id" jdbcType="VARCHAR" />
        <result property="name" column="name" jdbcType="VARCHAR" />
        <result property="key" column="model_key" jdbcType="VARCHAR" />
        <result property="description" column="description" jdbcType="VARCHAR" />
        <result property="comment" column="model_comment" jdbcType="VARCHAR" />
        <result property="created" column="created" jdbcType="TIMESTAMP" />
        <result property="createdBy" column="created_by" jdbcType="VARCHAR" />
        <result property="lastUpdated" column="last_updated" jdbcType="TIMESTAMP" />
        <result property="lastUpdatedBy" column="last_updated_by" jdbcType="VARCHAR" />
        <result property="version" column="version" jdbcType="INTEGER" />
        <result property="modelEditorJson" column="model_editor_json" jdbcType="VARCHAR" />
        <result property="modelType" column="model_type" jdbcType="INTEGER" />
        <result property="thumbnail" column="thumbnail" jdbcType="${blobType}" />
        <result property="tenantId" column="tenant_id" jdbcType="VARCHAR" />
        <result property="status" column="status" jdbcType="VARCHAR" />
    </resultMap>

    <select id="selectModelExtByParameters" parameterType="map" resultMap="modelResultMap">
        select m.*, w.status from ACT_DE_MODEL m, t_bill_workflow w
        where  m.id = w.flowable_model_id
        	AND w.del_flag = 0
            <if test="modelType != null">
               and m.model_type = #{modelType, jdbcType=VARCHAR}
            </if>
            <if test="filter != null">
               and (m.name like #{filter, jdbcType=VARCHAR} or m.description like #{filter, jdbcType=VARCHAR}) 
            </if>
            <if test="key != null">
               and m.model_key = #{key, jdbcType=VARCHAR}
            </if>
            <if test="tenantId != null and tenantId != ''">
               and m.tenant_id = #{tenantId, jdbcType=VARCHAR}
            </if>
            <if test="billCode != null and billCode != ''">
               and w.bill_code = #{billCode, jdbcType=VARCHAR}
            </if>
               and w.trans_type_code = #{transTypeCode, jdbcType=VARCHAR}
        <if test="sort != null">
            <if test="sort == 'nameAsc'">
                order by name asc
            </if>
            <if test="sort == 'nameDesc'">
                order by name desc
            </if>
            <if test="sort == 'modifiedAsc'">
                order by last_updated asc
            </if>
            <if test="sort == 'modifiedDesc'">
                order by last_updated desc
            </if>
        </if>
    </select>

</mapper>
