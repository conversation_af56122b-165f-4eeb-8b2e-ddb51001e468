package com.jeeplus.flowable.common.handler;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import com.jeeplus.extension.domain.BillMetadata;
import com.jeeplus.extension.domain.BillWorkflowTask;
import com.jeeplus.extension.domain.TaskDefExtension;
import com.jeeplus.extension.service.BillMetadataService;
import com.jeeplus.extension.service.BillWorkflowTaskService;
import com.jeeplus.extension.service.TaskDefExtensionService;
import com.jeeplus.extension.service.dto.FlowAssigneeDTO;
import com.jeeplus.flowable.model.BillWorkflow;
import com.jeeplus.flowable.service.BillLimitService;
import com.jeeplus.flowable.service.BillWorkflowService;
import com.jeeplus.flowable.utils.FlowableUtils;
import com.jeeplus.fls.PersonService;
import com.jeeplus.fls.RoleGroupService;
import com.jeeplus.fls.nc.api.domain.BasePerson;
import com.jeeplus.fls.nc.api.domain.BaseRoledata;
import com.jeeplus.fls.nc.api.domain.BaseRoledataBizunit;
import com.jeeplus.fls.nc.api.domain.BaseUser;
import com.jeeplus.fls.nc.api.service.BaseRoledataBizunitService;
import com.jeeplus.fls.nc.api.service.BaseRoledataService;
import com.jeeplus.sys.constant.CommonConstants;
import lombok.extern.slf4j.Slf4j;
import org.flowable.bpmn.model.FlowElement;
import org.flowable.bpmn.model.Process;
import org.flowable.bpmn.model.UserTask;
import org.flowable.common.engine.impl.el.ExpressionManager;
import org.flowable.engine.FormService;
import org.flowable.engine.RepositoryService;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.form.FormProperty;
import org.flowable.engine.form.TaskFormData;
import org.flowable.engine.impl.bpmn.behavior.UserTaskActivityBehavior;
import org.flowable.engine.impl.cfg.ProcessEngineConfigurationImpl;
import org.flowable.identitylink.api.IdentityLinkType;
import org.flowable.task.service.TaskService;
import org.flowable.task.service.impl.persistence.entity.TaskEntity;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class ExtUserTaskActivityBehavior extends UserTaskActivityBehavior {

    private static final long serialVersionUID = 7711531472879418236L;

    public ExtUserTaskActivityBehavior(UserTask userTask) {
        super(userTask);
    }


    /**
     * 分配办理人员
     */
    @Override
    protected void handleAssignments(TaskService taskService, String assignee, String owner, List<String> candidateUsers, List<String> candidateGroups, TaskEntity task, ExpressionManager expressionManager, DelegateExecution execution, ProcessEngineConfigurationImpl processEngineConfiguration) {
        Process process = SpringUtil.getBean(RepositoryService.class).getBpmnModel(task.getProcessDefinitionId()).getMainProcess();
        FlowElement flowElement = process.getFlowElement(task.getTaskDefinitionKey());
        Boolean isMultiInstance = FlowableUtils.isFlowElementMultiInstance(flowElement);
        if (isMultiInstance) {
            super.handleAssignments(taskService, assignee, owner, candidateUsers, candidateGroups, task, expressionManager, execution, processEngineConfiguration);
            return;
        }
        List<TaskDefExtension> list = SpringUtil.getBean(TaskDefExtensionService.class)
                .lambdaQuery()
                .eq(TaskDefExtension::getProcessDefId, process.getId())
                .eq(TaskDefExtension::getTaskDefId, task.getTaskDefinitionKey()).list();
        HashSet<String> candidateUserIds = new LinkedHashSet<>();

        TaskFormData taskFormData = SpringUtil.getBean(FormService.class).getTaskFormData(task.getId());
        List<String> buyers = getUserIdList(taskFormData);
        List<String> idOrgs = targetOrgIdList(taskFormData);
        List<String> idBizunits = targetBizunitIdList(taskFormData);
        List<String> idServerBizunits = idBizunitServer(taskFormData);
        if (!ObjectUtils.isEmpty(idServerBizunits)) {
            idBizunits = idServerBizunits;
        }
        List<String> idUseBizunits = idBizunitUse(taskFormData);
        if (!ObjectUtils.isEmpty(idUseBizunits)) {
            idBizunits = idUseBizunits;
        }
        //暂时不启用组织限定
        if (list.size() > 0) {
            TaskDefExtension taskDefExtension = list.get(0);
            // 获取流程审批节点扩展
            BillWorkflowTask billTask = SpringUtil.getBean(BillWorkflowTaskService.class).lambdaQuery().eq(BillWorkflowTask::getTaskDefId, taskDefExtension.getId()).one();
            String limitType = null;
            String limitVal = null;
            if (billTask != null) {
                limitType = billTask.getLimitType();
                // 通过配置的KEY 获取请求参数中的值
                limitVal = this.getFormValueByKey(taskFormData, billTask.getLimitVal());
            }
            // 如果审批节点没有配置限定 则获取默认限定
            if (StrUtil.isEmpty(limitType) && StrUtil.isEmpty(limitVal)) {
                BillWorkflow billWork = SpringUtil.getBean(BillWorkflowService.class).lambdaQuery()
                        .eq(BillWorkflow::getFlowableModelId, process.getId())
                        .eq(BillWorkflow::getDelFlag, CommonConstants.NO).one();
                if (billWork != null) {
                    BillMetadata billMetadata = SpringUtil.getBean(BillMetadataService.class).lambdaQuery()
                            .eq(BillMetadata::getBillCode, billWork.getBillCode())
                            .eq(BillMetadata::getDelFlag, CommonConstants.NO).one();
                    // 使用默认限定
                    if (billMetadata != null && ObjectUtils.isEmpty(limitType) && ObjectUtils.isEmpty(limitVal)) {
                        limitType = billMetadata.getDefaultLimitType();
                        limitVal = this.getFormValueByKey(taskFormData, billMetadata.getDefaultLimitVal());
                    }
                }
            }
            List<FlowAssigneeDTO> assigneeList = SpringUtil.getBean(TaskDefExtensionService.class).getById(taskDefExtension.getId()).getFlowAssigneeList();

            // 修改支持的审核角色类型  只保留 操作员、角色、角色组、流程用户组、岗位
            for (FlowAssigneeDTO flowAssignee : assigneeList) {
                switch (flowAssignee.getType()) {
                    case "user":
                        //转为员工id
                        List<String> idUserList = Arrays.asList(flowAssignee.getValue().split(","));
                        List<BasePerson> basePersonList = SpringUtil.getBean(PersonService.class).getPersonByUserId(idUserList);
                        //转为用户id，一个员工可能匹配多个用户
                        List<String> idPersonList = basePersonList.stream().map(BasePerson::getIdPerson).collect(Collectors.toList());
                        List<BaseUser> baseUserList = SpringUtil.getBean(PersonService.class).getUserByPersonId(idPersonList);
                        idUserList = baseUserList.stream().map(BaseUser::getIdUser).distinct().collect(Collectors.toList());
                        candidateUserIds.addAll(idUserList);
                        break;
//                    case "company":
//                        if (StrUtil.isNotBlank(flowAssignee.getValue())) {
//                            String companyId = flowAssignee.getValue();
//                            //公司匹配负责人
//                            List<BaseUser> userList = SpringUtil.getBean(PersonService.class).getOwerByOrgId(companyId);
//                            candidateUserIds.addAll(userList.stream().map(BaseUser::getIdUser).collect(Collectors.toList()));
//                        }
//
//                        break;
//                    case "depart":
//                        if (StrUtil.isNotBlank(flowAssignee.getValue())) {
//                            String officeId = flowAssignee.getValue();
//                            //同组织,部门负责人
//                            List<BaseUser> userList = SpringUtil.getBean(PersonService.class).getOwerByDeptId(officeId, new ArrayList<>(idOrgs));
//                            candidateUserIds.addAll(userList.stream().map(BaseUser::getIdUser).collect(Collectors.toList()));
//                        }
//
//                        break;
                    case "role":
                        if (StrUtil.isNotBlank(flowAssignee.getValue())) {
                            String[] roleIds = flowAssignee.getValue().split(",");
                            List<String> roleIdRes = Arrays.asList(roleIds);
                            // 20240920 优先走限定
                            if (StrUtil.isNotEmpty(limitType) && StrUtil.isNotEmpty(limitVal)) {
                                log.info(String.format("----------  角色限定 执行开始--------------- limitType = %s, limitVal = %s, processId = %s", limitType, limitVal, process.getId()));
                                roleIdRes = SpringUtil.getBean(BillLimitService.class).getRoleByLimit(flowAssignee.getValue(), limitType, limitVal);
                                log.info("----------  角色限定 执行结束---------------");
                            } else if (!ObjectUtils.isEmpty(idBizunits)) {
                                //1、有主体限定，通过组织匹配角色权限
                                //2、通过角色，查询用户
                                List<BaseRoledataBizunit> bizunits = new ArrayList<>(0);
                                for (String idBizunit : idBizunits) {
                                    List<BaseRoledataBizunit> baseRoledataBizunits = SpringUtil.getBean(BaseRoledataBizunitService.class).queryByBizunitAndRoles(idBizunit, Arrays.asList(roleIds));
                                    bizunits.addAll(baseRoledataBizunits);
                                }
                                roleIdRes = bizunits.stream().map(BaseRoledataBizunit::getIdRole).distinct().collect(Collectors.toList());
                            } else if (!ObjectUtils.isEmpty(idOrgs)) {
                                //通过组织角色查询
                                List<BaseRoledata> roledataList = new ArrayList<>(0);
                                for (String idOrg : idOrgs) {
                                    List<BaseRoledata> baseRoledataList = SpringUtil.getBean(BaseRoledataService.class).queryByOrgAndRoles(idOrg, Arrays.asList(roleIds));
                                    roledataList.addAll(baseRoledataList);
                                }
                                roleIdRes = roledataList.stream().map(BaseRoledata::getIdRole).distinct().collect(Collectors.toList());
                            }
                            if (ObjectUtils.isEmpty(roleIdRes)) {
                                throw new RuntimeException("未找到下一节点审批人");
                            }
                            List<BaseUser> userList = SpringUtil.getBean(PersonService.class).getUserByRoleIds(roleIdRes);
                            if (ObjectUtils.isEmpty(userList)) {
                                throw new RuntimeException("未找到下一节点审批人");
                            }
                            candidateUserIds.addAll(userList.stream().map(BaseUser::getIdUser).collect(Collectors.toList()));
                        }
                        break;
                    case "applyUserId":
                        candidateUserIds.add("${applyUserId}");
                        break;
                    case "system":
                        candidateUserIds.add("system");
                        break;
//                    case "previousExecutor":
//                        HistoricTaskInstance lastHisTask = SpringUtil.getBean(HistoryService.class).createHistoricTaskInstanceQuery().processInstanceId(task.getProcessInstanceId()).finished()
//                                .includeProcessVariables().orderByHistoricTaskInstanceEndTime().desc().list().get(0);
//
//                        candidateUserIds.add(lastHisTask.getAssignee());
//                        break;
//                    case "currentUserId":
//                        candidateUserIds.add(UserUtils.getCurrentUserDTO().getId());
//                        break;
//                    case "sql":
//                        Map userMap = SpringUtil.getBean(JdbcTemplate.class).queryForMap(flowAssignee.getValue());
//                        candidateUserIds.add(userMap.get("id").toString());
//                        break;
//                    case "custom":
                    //根据你的自定义标记，请自行实现
//                        List<String> bizUnitIdList = targetBizunitIdList(taskFormData);
//                        List <BaseUser> userList = SpringUtil.getBean ( PersonService.class ).getOwerByBizUnitIds (new ArrayList<>(bizUnitIdList));
//                        candidateUserIds.addAll(userList.stream().map(BaseUser::getIdUser).collect(Collectors.toList()));
//                        break;
                    case "roleGroup":
                        RoleGroupService roleGroupService = SpringUtil.getBean(RoleGroupService.class);
                        List<BaseUser> roleGroupUserList = roleGroupService.getUserByRoleGroupCode(flowAssignee.getValue(), limitType, limitVal);
                        if (ObjectUtils.isEmpty(roleGroupUserList)) {
                            throw new RuntimeException("未找到下一节点审批人");
                        }
                        candidateUserIds.addAll(roleGroupUserList.stream().map(BaseUser::getIdUser).collect(Collectors.toList()));
                        break;
                    case "processUserGroup":
                        RoleGroupService processUserGroupService = SpringUtil.getBean(RoleGroupService.class);
                        List<BaseUser> processUserGroupUserList = processUserGroupService.getUserByProcessUserGroupCode(flowAssignee.getValue(), limitType, limitVal);
                        if (ObjectUtils.isEmpty(processUserGroupUserList)) {
                            throw new RuntimeException("未找到下一节点审批人");
                        }
                        candidateUserIds.addAll(processUserGroupUserList.stream().map(BaseUser::getIdUser).collect(Collectors.toList()));
                        break;
                    case "post":
                        // 岗位
                        if (StrUtil.isNotBlank(flowAssignee.getValue())) {
                            String postId = flowAssignee.getValue();
                            // 兼容处理 如果传了组织参数 则走现有逻辑 没传则走新逻辑
                            List<BaseUser> userList = null;
                            // 优先走限定
                            if (StrUtil.isNotEmpty(limitType) && StrUtil.isNotEmpty(limitVal)) {
                                // 新逻辑 走限定模式
                                log.info("----------  岗位限定 执行开始---------------");
                                userList = SpringUtil.getBean(BillLimitService.class).getUserFromPostByLimit(postId, limitType, limitVal);
                                log.info("----------  岗位限定 执行结束---------------");
                            } else if (!ObjectUtils.isEmpty(idOrgs)) {
                                // 同组织岗位匹配用户
                                userList = SpringUtil.getBean(PersonService.class).getUserByPostId(postId, new ArrayList<>(idOrgs));
                            }
                            if (ObjectUtils.isEmpty(userList)) {
//                                throw new RuntimeException("未找到下一节点审批人");
                                break;
                            }
                            candidateUserIds.addAll(userList.stream().map(BaseUser::getIdUser).collect(Collectors.toList()));
                        }
                        break;
                    case "accurateUser":
                        String assigneeUserIds = task.getVariable("assigneeUserIds", String.class);
                        if (ObjectUtils.isEmpty(assigneeUserIds)) {
                            throw new RuntimeException("未找到下一节点待办人");
                        }
                        List<String> splitUserIds = StrUtil.split(assigneeUserIds, ",");
                        candidateUserIds.addAll(splitUserIds);
                        break;
                    case "workteam":
                        String idWorkteam = flowAssignee.getValue();
                        //限定条件逻辑
                        List<String> teamMembers = SpringUtil.getBean(BillLimitService.class).getMembers(idWorkteam, limitType, limitVal);
                        if (ObjectUtil.isNotEmpty(teamMembers)) {
                            //批量添加协办人
                            for (String memberId : teamMembers) {
                                // 为当前任务添加协办人
                                task.addUserIdentityLink(memberId, IdentityLinkType.PARTICIPANT);
                            }
                        }
                        break;
                    default:
                }
            }
        }
        List<String> candidateIds = new ArrayList<>(candidateUserIds);
        //此处可以根据业务逻辑自定义
        if (candidateIds.isEmpty()) {
            super.handleAssignments(taskService, null, owner, Lists.newArrayList(), Lists.newArrayList(), task, expressionManager, execution, processEngineConfiguration);
        } else if (candidateIds.size() == 1) {
            super.handleAssignments(taskService, candidateIds.get(0), owner, Lists.newArrayList(), Lists.newArrayList(), task, expressionManager, execution, processEngineConfiguration);
        } else {
            super.handleAssignments(taskService, null, owner, candidateIds, null, task, expressionManager, execution, processEngineConfiguration);
        }
    }

    private List<String> targetBizunitIdList(TaskFormData taskFormData) {
        //获取idOrg值，该值为所有表单统一定义规范，必须传入
        Set<String> bizunitId = new HashSet<>(0);
        //如果有指定组织则使用指定组织
        int targetOrgIdCount = taskFormData.getFormProperties().stream()
                .filter(it -> "targetBizunitCount".equals(it.getId()))
                .filter(it -> !ObjectUtils.isEmpty(it.getValue()))
                .mapToInt(it -> Integer.parseInt(it.getValue()))
                .findFirst().orElse(0);
        if (targetOrgIdCount > 0) {
            String targetOrgIds = taskFormData.getFormProperties().stream()
                    .filter(it -> "targetBizunitIds".equals(it.getId()))
                    .map(FormProperty::getValue)
                    .filter(value -> !ObjectUtils.isEmpty(value))
                    .findFirst().orElse(null);
            if (!ObjectUtils.isEmpty(targetOrgIds)) {
                bizunitId = new HashSet<>(JSONUtil.toList(targetOrgIds, String.class));
            }
        }
        return new ArrayList<>(bizunitId);
    }


    /**
     * 服务主体
     */
    private List<String> idBizunitServer(TaskFormData taskFormData) {
        return taskFormData.getFormProperties().stream()
                .filter(it -> "idServicePrincipal".equals(it.getId()))
                .map(FormProperty::getValue)
                .filter(value -> !ObjectUtils.isEmpty(value))
                .collect(Collectors.toList());
    }

    /**
     * 使用主体
     */
    private List<String> idBizunitUse(TaskFormData taskFormData) {
        return taskFormData.getFormProperties().stream()
                .filter(it -> "idBizunitUse".equals(it.getId()))
                .map(FormProperty::getValue)
                .filter(value -> !ObjectUtils.isEmpty(value))
                .collect(Collectors.toList());
    }

    private String getFormValueByKey(TaskFormData taskFormData, String key) {
        if (ObjectUtils.isEmpty(key)) {
            return null;
        }
        for (FormProperty formProperty : taskFormData.getFormProperties()) {
            if (key.equals(formProperty.getId())) {
                return formProperty.getValue();
            }
        }
        return null;
    }

    private List<String> targetOrgIdList(TaskFormData taskFormData) {
        //获取idOrg值，该值为所有表单统一定义规范，必须传入
        String idOrg = taskFormData.getFormProperties().stream()
                .filter(it -> "idOrg".equals(it.getId()))
                .map(FormProperty::getValue)
                .filter(value -> !ObjectUtils.isEmpty(value))
                .findFirst().orElse(null);
        Set<String> idOrgs = new HashSet<>(0);
        if (!ObjectUtils.isEmpty(idOrg)) {
            idOrgs = Collections.singleton(idOrg);
        }


        //如果有指定组织则使用指定组织
        int targetOrgIdCount = taskFormData.getFormProperties().stream()
                .filter(it -> "targetOrgIdCount".equals(it.getId()))
                .filter(it -> !ObjectUtils.isEmpty(it.getValue()))
                .mapToInt(it -> Integer.parseInt(it.getValue()))
                .findFirst().orElse(0);
        if (targetOrgIdCount > 0) {
            String targetOrgIds = taskFormData.getFormProperties().stream()
                    .filter(it -> "targetOrgIds".equals(it.getId()))
                    .map(FormProperty::getValue)
                    .findFirst().orElse(null);
            if (!ObjectUtils.isEmpty(targetOrgIds)) {
                idOrgs = new HashSet<>(JSONUtil.toList(targetOrgIds, String.class));
            }
        }
        return new ArrayList<>(idOrgs);
    }

    /**
     * 采购成员
     */
    private List<String> getUserIdList(TaskFormData taskFormData) {
        String userIdStr = "";
        List<FormProperty> formDatas = taskFormData.getFormProperties();
        for (FormProperty formData : formDatas) {
            if (formData.getId().equals("userIdStr")) {
                userIdStr = formData.getValue();
            }
        }
        List<String> userIdList = null;
        if (!ObjectUtils.isEmpty(userIdStr)) {
            userIdList = Arrays.asList(userIdStr.split(","));
        }
        return userIdList;
    }
}

