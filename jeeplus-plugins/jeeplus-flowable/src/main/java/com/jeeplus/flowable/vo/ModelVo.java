package com.jeeplus.flowable.vo;

import org.flowable.ui.modeler.model.ModelRepresentation;

public class ModelVo extends ModelRepresentation{
	/**
	 * 扩展单据编码、单据名称、交易类型编码、交易类型名称
	 */
	private String billCode;
	
	private String billName;
	
	private String tranTypeCode;
	
	private String tranTypeName;
	
	private String ver;

	public String getBillCode() {
		return billCode;
	}

	public void setBillCode(String billCode) {
		this.billCode = billCode;
	}

	public String getBillName() {
		return billName;
	}

	public void setBillName(String billName) {
		this.billName = billName;
	}

	public String getVer() {
		return ver;
	}

	public void setVer(String ver) {
		this.ver = ver;
	}

	public String getTranTypeCode() {
		return tranTypeCode;
	}

	public void setTranTypeCode(String tranTypeCode) {
		this.tranTypeCode = tranTypeCode;
	}

	public String getTranTypeName() {
		return tranTypeName;
	}

	public void setTranTypeName(String tranTypeName) {
		this.tranTypeName = tranTypeName;
	}
}
