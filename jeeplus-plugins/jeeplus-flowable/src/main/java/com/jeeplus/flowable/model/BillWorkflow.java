package com.jeeplus.flowable.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jeeplus.core.domain.BaseEntity;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 单据流程关系扩展
 * <AUTHOR>
 *
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("t_bill_workflow")
public class BillWorkflow extends BaseEntity{
	
	private String billCode;
	
	private String billName;
	
	private String transTypeCode;
	
	private String transTypeName;
	
	private String version;
	
	private String flowableModelId;
	
	private String flowableModelKey;
	
	private String status;
}
