package com.jeeplus.flowable.common.handler;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.jeeplus.extension.domain.BillWorkflowTask;
import com.jeeplus.extension.domain.TaskDefExtension;
import com.jeeplus.extension.service.BillWorkflowTaskService;
import com.jeeplus.extension.service.TaskDefExtensionService;
import com.jeeplus.extension.service.dto.FlowAssigneeDTO;
import com.jeeplus.flowable.service.BillLimitService;
import com.jeeplus.fls.PersonService;
import com.jeeplus.fls.nc.api.domain.BaseRoledata;
import com.jeeplus.fls.nc.api.domain.BaseRoledataBizunit;
import com.jeeplus.fls.nc.api.domain.BaseUser;
import com.jeeplus.fls.nc.api.service.BaseRoledataBizunitService;
import com.jeeplus.fls.nc.api.service.BaseRoledataService;
import com.jeeplus.sys.domain.User;
import com.jeeplus.sys.service.UserService;
import com.jeeplus.sys.service.dto.UserDTO;
import com.jeeplus.sys.utils.UserUtils;
import org.flowable.bpmn.model.Process;
import org.flowable.engine.FormService;
import org.flowable.engine.HistoryService;
import org.flowable.engine.RepositoryService;
import org.flowable.engine.TaskService;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.form.FormProperty;
import org.flowable.engine.form.TaskFormData;
import org.flowable.task.api.Task;
import org.flowable.task.api.TaskQuery;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class MutiInstanceHandler {
    @Resource
    private TaskService taskService;

    public Task getTaskByProcessInsId(String insId) {
        // =============== 查询任务  ===============
        TaskQuery todoTaskQuery = taskService.createTaskQuery()
                .includeProcessVariables()
                .processInstanceId(insId)
                .orderByTaskCreateTime().desc();
        List<Task> taskList = todoTaskQuery.list();
        if (ObjectUtils.isEmpty(taskList)) {
            return null;
        }
        if (taskList.size() > 1) {
            String userId = UserUtils.getCurrentUserDTO().getId();
            for(Task task:taskList){
                String assignee = task.getAssignee();
                if(userId.equals(assignee)){
                    return task;
                }
            }
        }
        return taskList.get(0);
    }

    /**
     * 获得当前节点的处理者列表
     *
     * @param execution 当前执行实例
     * @return 处理者列表
     */
    public List<String> getList(DelegateExecution execution) {
        String taskDefId = execution.getCurrentFlowElement().getId();
        Process process = SpringUtil.getBean(RepositoryService.class).getBpmnModel(execution.getProcessDefinitionId()).getMainProcess();
        List<TaskDefExtension> list = SpringUtil.getBean(TaskDefExtensionService.class).lambdaQuery()
                .eq(TaskDefExtension::getProcessDefId, process.getId())
                .eq(TaskDefExtension::getTaskDefId, taskDefId)
                .list();
        HashSet<String> candidateUserIds = new LinkedHashSet<>();
        Task task = getTaskByProcessInsId(execution.getProcessInstanceId());
        if (ObjectUtils.isEmpty(task)) {
            throw new RuntimeException("任务不存在");
        }
        TaskFormData taskFormData = SpringUtil.getBean(FormService.class).getTaskFormData(task.getId());
        List<String> buyers = getUserIdList(taskFormData);
        List<String> idOrgs = targetOrgIdList(taskFormData);
        List<String> idBizunits = targetBizunitIdList(taskFormData);
        List<String> idServerBizunits = idBizunitServer(taskFormData);
        if (!ObjectUtils.isEmpty(idServerBizunits)) {
            idBizunits = idServerBizunits;
        }
        List<String> idUseBizunits = idBizunitUse(taskFormData);
        if (!ObjectUtils.isEmpty(idUseBizunits)) {
            idBizunits = idUseBizunits;
        }
        if (list.size() > 0) {
            TaskDefExtension taskDefExtension = list.get(0);
            List<FlowAssigneeDTO> assigneeList = SpringUtil.getBean(TaskDefExtensionService.class).getById(taskDefExtension.getId()).getFlowAssigneeList();
            // 获取流程审批节点扩展
            BillWorkflowTask billTask = SpringUtil.getBean(BillWorkflowTaskService.class).lambdaQuery().eq(BillWorkflowTask::getTaskDefId, taskDefExtension.getId()).one();
            String limitType = null;
            String limitVal = null;
            if (billTask != null) {
                limitType = billTask.getLimitType();
                // 通过配置的KEY 获取请求参数中的值
                limitVal = this.getFormValueByKey(taskFormData, billTask.getLimitVal());
            }
            for (FlowAssigneeDTO flowAssignee : assigneeList) {
                switch (flowAssignee.getType()) {
                    case "user":
                        candidateUserIds.addAll(Arrays.asList(flowAssignee.getValue().split(",")));
                        break;
                    case "post":
                        if (StrUtil.isNotBlank(flowAssignee.getValue())) {
                            String postId = flowAssignee.getValue();
                            List<UserDTO> userList = SpringUtil.getBean(UserService.class).findListByPostId(postId);
                            candidateUserIds.addAll(CollectionUtils.extractToList(userList, "id"));
                        }

                        break;
                    case "company":
                        if (StrUtil.isNotBlank(flowAssignee.getValue())) {
                            String companyId = flowAssignee.getValue();
                            List<User> userList = SpringUtil.getBean(UserService.class).lambdaQuery().eq(User::getCompanyId, companyId).list();
                            candidateUserIds.addAll(CollectionUtils.extractToList(userList, "id"));
                        }

                        break;
                    case "depart":
                        if (StrUtil.isNotBlank(flowAssignee.getValue())) {
                            String officeId = flowAssignee.getValue();
                            List<User> userList = SpringUtil.getBean(UserService.class).lambdaQuery().eq(User::getOfficeId, officeId).list();
                            candidateUserIds.addAll(CollectionUtils.extractToList(userList, "id"));
                        }

                        break;
                    case "role":
                        if (StrUtil.isNotBlank(flowAssignee.getValue())) {
                            String totalAmount = "";
                            String iuseName = "";
                            List<FormProperty> formDatas = taskFormData.getFormProperties();
                            for(FormProperty formData:formDatas){
                                if(formData.getId().equals("totalAmount")){
                                    totalAmount = formData.getValue();
                                }
                                if(formData.getId().equals("iuseName")){
                                    iuseName = formData.getValue();
                                }
                            }

                            // 外采申请  采购金额大于等于200 && 用途不等于租赁  代表下一步审核是 采购对口
                            if((task.getName().equals("分公司经理") || task.getName().equals("监督科")) && !ObjectUtils.isEmpty(totalAmount)
                                    && BigDecimal.valueOf(Double.parseDouble(totalAmount)).compareTo(BigDecimal.valueOf(200)) > -1){
                                candidateUserIds.addAll(buyers);
                            } else {
                                String[] roleIds = flowAssignee.getValue().split(",");
                                List<String> roleIdRes = Arrays.asList(roleIds);
                                if (!ObjectUtils.isEmpty(idBizunits)) {
                                    //1、有主体限定，通过组织匹配角色权限
                                    //2、通过角色，查询用户
                                    List<BaseRoledataBizunit> bizunits = new ArrayList<>(0);
                                    for (String idBizunit : idBizunits) {
                                        List<BaseRoledataBizunit> baseRoledataBizunits = SpringUtil.getBean(BaseRoledataBizunitService.class).queryByBizunitAndRoles(idBizunit, Arrays.asList(roleIds));
                                        bizunits.addAll(baseRoledataBizunits);
                                    }
                                    roleIdRes = bizunits.stream().map(BaseRoledataBizunit::getIdRole).distinct().collect(Collectors.toList());
                                } else if (!ObjectUtils.isEmpty(idOrgs)) {
                                    //通过组织角色查询
                                    List<BaseRoledata> roledataList = new ArrayList<>(0);
                                    for (String idOrg : idOrgs) {
                                        List<BaseRoledata> baseRoledataList = SpringUtil.getBean(BaseRoledataService.class).queryByOrgAndRoles(idOrg, Arrays.asList(roleIds));
                                        roledataList.addAll(baseRoledataList);
                                    }
                                    roleIdRes = roledataList.stream().map(BaseRoledata::getIdRole).distinct().collect(Collectors.toList());
                                } else {
                                    // 兼容处理  优先保留现有流程条件 如果以参数传过来限定经营主体、组织则按以前逻辑走
                                    // 如果参数未传 则走新逻辑
                                    roleIdRes = SpringUtil.getBean(BillLimitService.class).getRoleByLimit(flowAssignee.getValue(), limitType, limitVal);
                                }
                                if (ObjectUtils.isEmpty(roleIdRes)) {
                                    throw new RuntimeException("未找到下一节点审批人");
                                }
                                List<BaseUser> userList = SpringUtil.getBean(PersonService.class).getUserByRoleIds(roleIdRes);
                                if (ObjectUtils.isEmpty(userList)) {
                                    throw new RuntimeException("未找到下一节点审批人");
                                }
                                candidateUserIds.addAll(userList.stream().map(BaseUser::getIdUser).collect(Collectors.toList()));
                            }
                        }
                        // ======================================================
//                        if ( StrUtil.isNotBlank ( flowAssignee.getValue ( ) ) ) {
//                            String[] roleIds = flowAssignee.getValue ( ).split ( "," );
//                            for (String roleId : roleIds) {
//                                List <UserDTO> userList = SpringUtil.getBean ( UserService.class ).findListByRoleId ( roleId );
//                                candidateUserIds.addAll ( CollectionUtils.extractToList ( userList, "id" ) );
//                            }
//                        }
                        break;
                    case "applyUserId":
                        candidateUserIds.add("${applyUserId}");
                        break;
                    case "previousExecutor":
                        HistoricTaskInstance lastHisTask = SpringUtil.getBean(HistoryService.class).createHistoricTaskInstanceQuery().processInstanceId(execution.getProcessInstanceId()).finished()
                                .includeProcessVariables().orderByHistoricTaskInstanceEndTime().desc().list().get(0);

                        candidateUserIds.add(lastHisTask.getAssignee());
                        break;
                    case "currentUserId":
                        candidateUserIds.add(UserUtils.getCurrentUserDTO().getId());
                        break;
                    case "sql":
                        Map userMap = SpringUtil.getBean(JdbcTemplate.class).queryForMap(flowAssignee.getValue());
                        candidateUserIds.add(userMap.get("id").toString());
                        break;
                    case "custom":
//                        if(flowAssignee.getValue ().equals ( "xxx" ))

                        //根据你的自定义标记，请自行实现
                        break;
                }
            }
        }

        return new ArrayList<>(candidateUserIds);

    }

    private String getFormValueByKey(TaskFormData taskFormData, String key) {
        if (ObjectUtils.isEmpty(key)) {
            return null;
        }
        for (FormProperty formProperty : taskFormData.getFormProperties()) {
            if (key.equals(formProperty.getId())) {
                return formProperty.getValue();
            }
        }
        return null;
    }

    /**
     * 获取会签是否结束
     *
     * @param execution 当前执行实例
     * @return 是否结束
     */
    public boolean getComplete(DelegateExecution execution) {
        Integer nrOfCompletedInstances = (Integer) execution.getVariable("nrOfCompletedInstances");
        Integer nrOfInstances = (Integer) execution.getVariable("nrOfInstances");
        int agreeCount = 0, rejectCount = 0, abstainCount = 0;
        Map<String, Object> vars = execution.getVariables();
        for (String key : vars.keySet()) {
            //会签投票以SIGN_VOTE+TaskId标识
            //获得会签投票的统计结果
//            if (key.contains(FlowConst.SIGN_VOTE) && !key.equals(FlowConst.SIGN_VOTE_RESULT)) {
//                Integer value = (Integer) vars.get(key);
//                //统计同意、驳回、弃权票数
//                //省略代码若干......
//            }
        }
        //以下为一段简单的规则，可以按情况实现自己的会签规则
        if (!nrOfCompletedInstances.equals(nrOfInstances)) {
            //必须等所有的办理人都投票
            return false;
        } else {
            //会签全部完成时,使用默认规则结束
            if (rejectCount > 0) {
                //有反对票，则最终的会签结果为不通过
                //移除SIGN_VOTE+TaskId为标识的参数
//                removeSignVars(execution);
                //增加会签结果参数，以便之后流转使用
//                execution.setVariable(FlowConst.SIGN_VOTE_RESULT, false);
                //会签结束
                return true;
            } else {
                //没有反对票时，则最终的会签结果为通过
//                removeSignVars(execution);
//                execution.setVariable(FlowConst.SIGN_VOTE_RESULT, true);
                return true;
            }
        }
    }


    private List<String> targetOrgIdList(TaskFormData taskFormData) {
        //获取idOrg值，该值为所有表单统一定义规范，必须传入
        String idOrg = taskFormData.getFormProperties().stream()
                .filter(it -> "idOrg".equals(it.getId()))
                .map(FormProperty::getValue)
                .findFirst().orElse(null);
        Set<String> idOrgs = new HashSet<>(0);
        if (!ObjectUtils.isEmpty(idOrg)) {
            idOrgs = Collections.singleton(idOrg);
        }


        //如果有指定组织则使用指定组织
        int targetOrgIdCount = taskFormData.getFormProperties().stream()
                .filter(it -> "targetOrgIdCount".equals(it.getId()))
                .filter(it -> !ObjectUtils.isEmpty(it.getValue()))
                .mapToInt(it -> Integer.parseInt(it.getValue()))
                .findFirst().orElse(0);
        if (targetOrgIdCount > 0) {
            String targetOrgIds = taskFormData.getFormProperties().stream()
                    .filter(it -> "targetOrgIds".equals(it.getId()))
                    .map(FormProperty::getValue)
                    .findFirst().orElse(null);
            if (!ObjectUtils.isEmpty(targetOrgIds)) {
                idOrgs = new HashSet<>(JSONUtil.toList(targetOrgIds, String.class));
            }
        }
        return new ArrayList<>(idOrgs);
    }


    private List<String> targetBizunitIdList(TaskFormData taskFormData) {
        //获取idOrg值，该值为所有表单统一定义规范，必须传入
        Set<String> bizunitId = new HashSet<>(0);
        //如果有指定组织则使用指定组织
        int targetOrgIdCount = taskFormData.getFormProperties().stream()
                .filter(it -> "targetBizunitCount".equals(it.getId()))
                .filter(it -> !ObjectUtils.isEmpty(it.getValue()))
                .mapToInt(it -> Integer.parseInt(it.getValue()))
                .findFirst().orElse(0);
        if (targetOrgIdCount > 0) {
            String targetOrgIds = taskFormData.getFormProperties().stream()
                    .filter(it -> "targetBizunitIds".equals(it.getId()))
                    .map(FormProperty::getValue)
                    .findFirst().orElse(null);
            if (!ObjectUtils.isEmpty(targetOrgIds)) {
                bizunitId = new HashSet<>(JSONUtil.toList(targetOrgIds, String.class));
            }
        }
        return new ArrayList<>(bizunitId);
    }


    /**
     * 服务主体
     */
    private List<String> idBizunitServer(TaskFormData taskFormData) {
        return taskFormData.getFormProperties().stream()
                .filter(it -> "idServicePrincipal".equals(it.getId()))
                .map(FormProperty::getValue)
                .filter(value -> !ObjectUtils.isEmpty(value))
                .collect(Collectors.toList());
    }

    /**
     * 使用主体
     */
    private List<String> idBizunitUse(TaskFormData taskFormData) {
        return taskFormData.getFormProperties().stream()
                .filter(it -> "idBizunitUse".equals(it.getId()))
                .map(FormProperty::getValue)
                .filter(value -> !ObjectUtils.isEmpty(value))
                .collect(Collectors.toList());
    }

    /**
     * 采购成员
     */
    private List<String> getUserIdList(TaskFormData taskFormData) {
        String userIdStr = "";
        List<FormProperty> formDatas = taskFormData.getFormProperties();
        for(FormProperty formData:formDatas){
            if(formData.getId().equals("userIdStr")){
                userIdStr = formData.getValue();
            }
        }
        List<String> userIdList = null;
        if(!ObjectUtils.isEmpty(userIdStr)){
            userIdList = Arrays.asList(userIdStr.split(","));
        }
        return userIdList;
    }
}
