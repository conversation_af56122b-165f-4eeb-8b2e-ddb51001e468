package com.jeeplus.flowable.service;

import cn.hutool.core.map.MapUtil;
import cn.hutool.http.HttpStatus;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.fls.openapi.sdk.facade.ApiClientFacade;
import com.jeeplus.fls.PersonService;
import com.jeeplus.fls.enums.LimitType;
import com.jeeplus.fls.nc.api.domain.*;
import com.jeeplus.fls.nc.api.service.BaseBizunitService;
import com.jeeplus.fls.nc.api.service.BasePersonService;
import com.jeeplus.fls.nc.api.service.BaseRoledataBizunitService;
import com.jeeplus.fls.nc.api.service.BaseRoledataService;
import com.jeeplus.sys.constant.CommonConstants;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 审核人员限定查询服务类
 *
 * <AUTHOR>
 *
 */
@Service
@Transactional(propagation = Propagation.NOT_SUPPORTED)
@DS("fls_db")
public class BillLimitService {
	
	@Resource
	private PersonService personService;
	
    @Resource
    private BasePersonService basePersonService;
    
    @Resource
    private BaseRoledataService baseRoledataService;
    
    @Resource
    private BaseRoledataBizunitService baseRoledataBizunitService;
    
    @Resource
    private BaseBizunitService baseBizunitService;

	@Resource
	private ApiClientFacade apiClientFacade;
    
    /**
     * 根据岗位ID和限定查询岗位用户
     * @param postId 岗位ID
     * @param limitType 限定类型
     * @param limitVal 限定值
     * @return List<BaseUser>
     */
    public List<BaseUser> getUserFromPostByLimit(String postId,String limitType , String limitVal){
    	// 1、获取选择的岗位ID
    	String [] postIds = postId.split(CommonConstants.SPLITOR);
    	// 2、根据岗位ID查询所有岗位名称
    	// 3、再根据岗位名称查询所有岗位ID
    	// 再根据
		List<BasePerson> basePersonList = null;
		LambdaQueryChainWrapper<BasePerson>  queryWrapper = basePersonService.lambdaQuery().eq(BasePerson::getStatus, "2")
				.in(BasePerson::getIdPost, Arrays.asList(postIds))
                .eq(BasePerson::getDeleteFlag, CommonConstants.NO);
		if (ObjectUtils.isEmpty(limitType) || ObjectUtils.isEmpty(limitVal)) {
			basePersonList = queryWrapper.list();
		} else {
			if (LimitType.ORG.getType().equals(limitType)) {
				basePersonList = queryWrapper.eq(BasePerson::getIdOrg, limitVal).list();
			} else if (LimitType.DEP.getType().equals(limitType)) {
				basePersonList = queryWrapper.eq(BasePerson::getIdDepartment, limitVal).list();
			} else if (LimitType.BIZ.getType().equals(limitType)) {
				// 查询经营主体
				BaseBizunit baseBizunit = baseBizunitService.lambdaQuery().eq(BaseBizunit::getIdBizunit, limitVal).one();
				if (!ObjectUtils.isEmpty(baseBizunit)) {
					basePersonList = queryWrapper.eq(BasePerson::getIdOrg, baseBizunit.getIdOrg()).list();
				}
			}
		}
		if (ObjectUtils.isEmpty(basePersonList)){
			return new ArrayList<>(0);
        }
		return personService.getUserByPersonId(basePersonList.stream().map(BasePerson::getIdPerson).collect(Collectors.toList()));
    }
    
    /**
     * 根据角色ID和限定查询角色用户
     * @param roleId 角色ID
     * @param limitType 限定类型
     * @param limitVal 限定值
     * @return List<BaseUser>
     */
    public List<String> getRoleByLimit(String roleId,String limitType , String limitVal){
    	String [] roleIds = roleId.split(CommonConstants.SPLITOR);
		List<String> roleIdRes = Arrays.asList(roleIds);
		List<BaseRoledata> roledataList = new ArrayList<>(0);
		if(!ObjectUtils.isEmpty(limitType) && !ObjectUtils.isEmpty(limitVal)) {	
			if(LimitType.ORG.getType().equals(limitType)) {
				roledataList = baseRoledataService.queryByOrgAndRoles(limitVal, Arrays.asList(roleIds));
				if(ObjectUtils.isEmpty(roledataList)) {
					throw new RuntimeException("未找到下一节点审批人");
				}
				roleIdRes = roledataList.stream().map(BaseRoledata::getIdRole).distinct().collect(Collectors.toList());
			} else if(LimitType.DEP.getType().equals(limitType)) {
				roledataList = baseRoledataService.queryByDepartAndRoles(limitVal,Arrays.asList(roleIds));
				if(ObjectUtils.isEmpty(roledataList)) {
					throw new RuntimeException("未找到下一节点审批人");
				}
				roleIdRes = roledataList.stream().map(BaseRoledata::getIdRole).distinct().collect(Collectors.toList());
			} else if(LimitType.BIZ.getType().equals(limitType)) {
                List<BaseRoledataBizunit> baseRoledataBizunits = this.baseRoledataBizunitService.queryByBizunitAndRoles(limitVal, Arrays.asList(roleIds));
				roleIdRes = baseRoledataBizunits.stream().map(BaseRoledataBizunit::getIdRole).distinct().collect(Collectors.toList());
			}
		}
        return roleIdRes;
    }

	/**
	 * 通过班组id和限定条件查询对应班组用户id列表
	 *
	 * @param idWorkTeam 工作班组id
	 * @param limitType  限定类型
	 * @param limitVal   限定值
	 * @return List<BaseUser>
	 */
	public List<String> getMembers(String idWorkTeam, String limitType, String limitVal) {
		Map<String, Object> params = MapUtil.builder(new HashMap<String, Object>()).put("idWorkteam", idWorkTeam).put("limitType", limitType).put("limitVal", limitVal).build();
		String result = apiClientFacade.invoke(HttpMethod.POST.name(), "/api/v1/mdms/work-team/member", params);
		JSONObject response = JSONUtil.toBean(result, JSONObject.class);
		if (response.getInt("code") != HttpStatus.HTTP_OK) {
			throw new RuntimeException("获取工作班组成员失败:" + response.getStr("msg"));
		}
		JSONArray dataArray = response.getJSONArray("data");
		List<String> userIds = new ArrayList<>();
		dataArray.forEach(item -> {
			JSONObject itemObj = (JSONObject)item;
			String userId = itemObj.getStr("userId");
			userIds.add(userId);
		});
		return userIds;
	}

	/**
	 * 通过班组id获取班组负责人
	 *
	 * @param idWorkTeam 工作班组id
	 * @return String 班组负责人id
	 */
	public String getTeamHeadman(String idWorkTeam) {
		Map<String, Object> params = MapUtil.builder(new HashMap<String, Object>()).put("idWorkteam", idWorkTeam).build();
		String result = apiClientFacade.invoke(HttpMethod.POST.name(), "/api/v1/mdms/work-team/detail", params);
		JSONObject response = JSONUtil.toBean(result, JSONObject.class);
		if (response.getInt("code") != HttpStatus.HTTP_OK) {
			throw new RuntimeException("获取工作班组详情失败:" + response.getStr("msg"));
		}
		JSONObject data = response.getJSONObject("data");
		return data.getStr("idHeadman");
	}
}
