package com.jeeplus.flowable.vo;

public enum ActionTypeEnum {
    /**
     * 重新提交
     */
    RECOMMIT(0, "reCommit"),

    /**
     * 驳回至申请人
     */
    REJECT_CREATOR(1, "REJECT_CREATOR"),

    /**
     * 驳回至上一级
     */
    REJECT(2, "REJECT");

    private final int code;

    private final String name;

    ActionTypeEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public int getCode() {
        return code;
    }

    public static ActionTypeEnum getEnumByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (ActionTypeEnum approveCodeEnum : ActionTypeEnum.values()) {
            if (approveCodeEnum.getCode() == code.intValue()) {
                return approveCodeEnum;
            }
        }
        return null;
    }
}
