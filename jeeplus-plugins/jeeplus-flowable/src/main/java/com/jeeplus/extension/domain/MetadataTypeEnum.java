package com.jeeplus.extension.domain;

public enum MetadataTypeEnum {
	ORGANIZATION("1", "organization"), 
	DEPARTMENT("2", "department"), 
	BUSINESSENTITY("3", "businessEntity");

	private final String code;

	private final String name;

	MetadataTypeEnum(String code, String name) {
		this.name = name;
		this.code = code;
	}

	public String getName() {
		return name;
	}

	public String getCode() {
		return code;
	}

	public static MetadataTypeEnum getEnumByName(String name) {
		if (name == null) {
			return null;
		}
		for (MetadataTypeEnum metadataTypeEnum : MetadataTypeEnum.values()) {
			if (metadataTypeEnum.getName().equalsIgnoreCase(name)) {
				return metadataTypeEnum;
			}
		}
		return null;
	}
}
