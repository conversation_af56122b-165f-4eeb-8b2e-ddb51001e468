<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jeeplus.extension.bizform.mapper.BizFormMapper">
    <select id="showTable" resultType="com.jeeplus.extension.bizform.dto.TableFieldDto">
--         SHOW COLUMNS FROM ${tableName};
        SELECT COLUMN_NAME as field, COLUMN_TYPE as type,COLUMN_COMMENT as comment FROM
            information_schema.`COLUMNS`
        WHERE TABLE_NAME = #{tableName}
    </select>
</mapper>
