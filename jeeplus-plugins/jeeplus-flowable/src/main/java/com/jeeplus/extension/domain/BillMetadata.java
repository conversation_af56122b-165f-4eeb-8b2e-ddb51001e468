package com.jeeplus.extension.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jeeplus.core.domain.BaseEntity;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 单据资源初始化信息表
 * 
 * <AUTHOR>
 *
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("t_bill_metadata")
public class BillMetadata extends BaseEntity {
	
	private String idResource;

	private String billCode;

	private String billName;

	private String metadata;

	private String defaultLimitType;

	private String defaultLimitVal;
	
	private String formdata;
	
	private String formId;

	private String primaryKey;

	private String bizunitKey;
}
