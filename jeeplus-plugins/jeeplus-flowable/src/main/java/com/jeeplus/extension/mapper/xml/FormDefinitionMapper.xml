<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jeeplus.extension.mapper.FormDefinitionMapper">

    <sql id="formDefinitionColumns">
        a.id AS "id",
		a.create_by_id AS "createBy.id",
		a.create_time AS "createTime",
		a.update_by_id AS "updateBy.id",
		a.update_time AS "updateTime",
		a.remarks AS "remarks",
		a.del_flag AS "delFlag",
		a.category_id AS "category.id",
		a.name AS "name",
		b.name AS "category.name"
    </sql>


    <select id="getById" resultType="com.jeeplus.extension.service.dto.FormDefinitionDTO">
        SELECT
        <include refid="formDefinitionColumns"/>
        FROM act_extension_form_def a
        LEFT JOIN act_extension_form_category b ON b.id = a.category_id
        WHERE a.id = #{id}
    </select>

    <select id="findList" resultType="com.jeeplus.extension.service.dto.FormDefinitionDTO">
        SELECT
        <include refid="formDefinitionColumns"/>,
        c.id AS "formDefinitionJson.id",
        c.json_body AS "formDefinitionJson.jsonBody",
        c.version AS "formDefinitionJson.version",
        c.status AS "formDefinitionJson.status",
        c.is_primary AS "formDefinitionJson.isPrimary"
        FROM act_extension_form_def a
        LEFT JOIN act_extension_form_category b ON b.id = a.category_id
        LEFT JOIN act_extension_form_def_json c ON c.form_definition_id = a.id
        ${ew.customSqlSegment}
    </select>


</mapper>
