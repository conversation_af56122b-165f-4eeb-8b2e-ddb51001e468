package com.jeeplus.extension.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jeeplus.core.domain.BaseEntity;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 流程任务定义扩展
 * 
 * <AUTHOR>
 *
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("t_bill_workflow_task")
public class BillWorkflowTask extends BaseEntity {

	private String taskDefId;

	private String flowableModelId;

	private String flowableModelKey;

	private String limitType;

	private String limitVal;
	
	private String taskId;
}
