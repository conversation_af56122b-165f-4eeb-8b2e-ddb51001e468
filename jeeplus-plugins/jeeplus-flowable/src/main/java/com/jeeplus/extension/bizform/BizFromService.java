package com.jeeplus.extension.bizform;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.jeeplus.extension.bizform.dto.TableFieldDto;
import com.jeeplus.extension.bizform.mapper.BizFormMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * created 2023/11/23 23:07:38
 */
@Service
public class BizFromService {
    @Resource
    private BizFormMapper bizFormMapper;

    @InterceptorIgnore
    public List<TableFieldDto> showTable(){
       List<TableFieldDto> fieldDtoList = bizFormMapper.showTable("t_repair_log_bill");
       return fieldDtoList;
    }
}
