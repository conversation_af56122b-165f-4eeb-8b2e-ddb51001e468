/**
 * Copyright © 2021-2025 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.datav.service;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jeeplus.datav.domain.DataComponent;
import com.jeeplus.datav.mapper.DataComponentMapper;

/**
 * 组件Service
 * <AUTHOR>
 * @version 2023-02-12
 */
@Service
@Transactional
public class DataComponentService extends ServiceImpl<DataComponentMapper, DataComponent> {

}
